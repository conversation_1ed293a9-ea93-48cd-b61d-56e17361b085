using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using TeklaTool.Models;
using TeklaTool.Services;
using TeklaTool.Utils;

namespace TeklaTool.ViewModels
{
    public class PartListViewModel : ViewModelBase, IDisposable
    {
        private TeklaModelService _teklaModelService;
        private MainViewModel _mainViewModel;
        private readonly AppConfig _config = AppConfig.Instance;
        private ObservableCollection<TeklaModelPart> _parts = new ObservableCollection<TeklaModelPart>();
        private ObservableCollection<MergedPartRow> _mergedParts = new ObservableCollection<MergedPartRow>();
        private bool _isMergeRows;
        private bool _disposed = false;
        public ObservableCollection<TeklaModelPart> Parts => _parts;
        public ObservableCollection<MergedPartRow> MergedParts => _mergedParts;
        public bool IsMergeRows
        {
            get => _isMergeRows;
            set
            {
                if (SetProperty(ref _isMergeRows, value))
                {
                    UpdateMergedParts();
                    OnPropertyChanged(nameof(PartsView));
                }
            }
        }
        public IEnumerable<object> PartsView => IsMergeRows ? (IEnumerable<object>)MergedParts : Parts;
        public PartListViewModel(TeklaModelService service, MainViewModel mainViewModel = null)
        {
            ExceptionHandler.SafeExecute(() =>
            {
                _teklaModelService = service;
                _mainViewModel = mainViewModel;
                Parts.CollectionChanged += (s, e) => { if (IsMergeRows && !_disposed) UpdateMergedParts(); };
                Logger.LogInfo("PartListViewModel初始化完成");
            }, "初始化PartListViewModel");
        }
        public void SetParts(IEnumerable<TeklaModelPart> parts)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                Parts.Clear();
                if (parts != null)
                {
                    foreach (var p in parts)
                    {
                        if (p != null) Parts.Add(p);
                    }
                }
                if (IsMergeRows) UpdateMergedParts();
                Logger.LogInfo($"设置零件数据，共 {Parts.Count} 个零件");
            }, "设置零件数据");
        }
        private void UpdateMergedParts()
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                // 清空合并行集合
                MergedParts.Clear();

            // 如果未开启合并行，直接返回
            if (!IsMergeRows) return;

            // 如果没有零件数据，直接返回
            if (Parts == null || Parts.Count == 0) return;

                // 按零件编号分组
                var grouped = Parts.GroupBy(p => p.PartNumber)
                    .Select((g, idx) => new MergedPartRow
                    {
                        Index = idx + 1,
                        PartNumber = g.Key,
                        Name = g.First().Name,
                        Profile = g.First().Profile,
                        Material = g.First().Material,
                        Finish = g.First().Finish,
                        Class = g.First().Class,
                        Phase = g.First().Phase,
                        Count = g.Count(),
                        AssemblyNumber = g.First().AssemblyNumber,
                        Guid = g.First().Guid,
                        ModelObjectIds = g.Where(x => x.ModelObjectId.HasValue).Select(x => x.ModelObjectId.Value).ToList(),
                        Remark = g.First().Remark
                    }).ToList();

                // 添加到合并行集合
                foreach (var row in grouped)
                {
                    MergedParts.Add(row);
                }

                // 通知视图更新
                OnPropertyChanged(nameof(PartsView));
                Logger.LogInfo($"更新合并行完成，共 {MergedParts.Count} 行");
            }, "更新合并行数据");
        }

        // 选择变更逻辑入口
        public void HandlePartSelectionChanged(IList<TeklaModelPart> selectedParts)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                // 直接调用高亮方法
                HighlightParts(selectedParts);
            }, "处理零件选择变更");
        }

        // 高亮逻辑
        public void HighlightParts(IList<TeklaModelPart> selectedParts)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                // 检查是否启用了高亮功能
                if (_mainViewModel != null && !_mainViewModel.EnableHighlight)
                {
                    return; // 如果禁用了高亮功能，直接返回
                }

                if (selectedParts == null || selectedParts.Count == 0)
                {
                    _teklaModelService?.HighlightObjects(new List<int>());
                    return;
                }

                // 根据是否开启了合并行来决定使用哪种高亮方式
                if (IsMergeRows)
                {
                    // 合并行模式：使用零件编号高亮
                    // 收集所有选中行的零件编号
                    var partNumbers = selectedParts.Select(p => p.PartNumber).Distinct().ToList();

                    // 找出所有具有这些零件编号的零件
                    var allParts = Parts.Where(p => partNumbers.Contains(p.PartNumber)).ToList();

                    // 提取这些零件的ID
                    var modelObjectIds = allParts.Where(p => p.ModelObjectId.HasValue)
                                                .Select(p => p.ModelObjectId.Value)
                                                .ToList();

                    if (modelObjectIds.Count > 0)
                    {
                        _teklaModelService?.HighlightObjects(modelObjectIds);
                        Logger.LogInfo($"高亮 {modelObjectIds.Count} 个零件（合并模式）");
                    }
                }
                else
                {
                    // 未合并行模式：直接使用零件ID高亮
                    var modelObjectIds = selectedParts.Where(p => p.ModelObjectId.HasValue)
                                                    .Select(p => p.ModelObjectId.Value)
                                                    .ToList();
                    if (modelObjectIds.Count > 0)
                    {
                        _teklaModelService?.HighlightObjects(modelObjectIds);
                        Logger.LogInfo($"高亮 {modelObjectIds.Count} 个零件（普通模式）");
                    }
                }
            }, "高亮零件");
        }

        // 筛选逻辑迁移（示例：按名称筛选）
        public void FilterByName(string name)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                var filtered = Parts.Where(p => p.Name != null && p.Name.Contains(name)).ToList();
                SetParts(filtered);
                Logger.LogInfo($"按名称筛选: {name}，结果: {filtered.Count} 个零件");
            }, "按名称筛选零件");
        }

        // 命令示例
        public ICommand FilterByNameCommand => new RelayCommand(param =>
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                if (param is string name)
                {
                    FilterByName(name);
                }
            }, "执行按名称筛选命令");
        }, param => !_disposed);

        // 搜索方法
        public void Search(string searchText)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // 如果搜索文本为空，显示所有零件
                    OnPropertyChanged(nameof(PartsView));
                    return;
                }

                // 这里可以实现具体的搜索逻辑
                // 例如：筛选包含搜索文本的零件
                Logger.LogInfo($"在零件列表中搜索: {searchText}");
            }, "搜索零件");
        }

        #region IDisposable Implementation

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    ExceptionHandler.SafeExecute(() =>
                    {
                        _parts?.Clear();
                        _mergedParts?.Clear();
                        Logger.LogInfo("PartListViewModel资源已释放");
                    }, "释放PartListViewModel资源");
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~PartListViewModel()
        {
            Dispose(false);
        }

        #endregion

        public class MergedPartRow
        {
            public int Index { get; set; }
            public string PartNumber { get; set; }
            public string Name { get; set; }
            public string Profile { get; set; }
            public string Material { get; set; }
            public string Finish { get; set; }
            public string Class { get; set; }
            public string Phase { get; set; }
            public int Count { get; set; }
            public string AssemblyNumber { get; set; }
            public string Guid { get; set; }
            public List<int> ModelObjectIds { get; set; }
            public string Remark { get; set; }
        }
    }
}
