using System;
using System.IO;
using Tekla.Structures;

namespace TeklaTool.Models
{
    public class ModelConnection
    {
        private Tekla.Structures.Model.Model _currentModel = new Tekla.Structures.Model.Model();

        public Tekla.Structures.Model.Model CurrentModel => _currentModel;

        public bool Connect()
        {
            try
            {
                return _currentModel.GetConnectionStatus();
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool IsConnected()
        {
            return _currentModel != null && _currentModel.GetConnectionStatus();
        }
    }
}
