using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace TeklaTool.Views
{
    public partial class FilterControl : UserControl, INotifyPropertyChanged
    {
        // 筛选按钮点击前事件 - 在筛选按钮点击前触发，用于更新筛选数据
        public event EventHandler FilterButton_PreClick;

        public class FilterItem : INotifyPropertyChanged
        {
            private string _text;
            private bool _isSelected;

            public string Text
            {
                get => _text;
                set
                {
                    if (_text != value)
                    {
                        _text = value;
                        OnPropertyChanged(nameof(Text));
                    }
                }
            }

            public bool IsSelected
            {
                get => _isSelected;
                set
                {
                    if (_isSelected != value)
                    {
                        _isSelected = value;
                        OnPropertyChanged(nameof(IsSelected));
                    }
                }
            }

            public event PropertyChangedEventHandler PropertyChanged;

            protected virtual void OnPropertyChanged(string propertyName)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        // 依赖属性
        public static readonly DependencyProperty ColumnNameProperty =
            DependencyProperty.Register("ColumnName", typeof(string), typeof(FilterControl), new PropertyMetadata(string.Empty));

        public static readonly DependencyProperty IsFilteredProperty =
            DependencyProperty.Register("IsFiltered", typeof(bool), typeof(FilterControl), new PropertyMetadata(false));

        // 事件
        public event EventHandler<FilterEventArgs> FilterChanged;

        // 属性
        public string ColumnName
        {
            get { return (string)GetValue(ColumnNameProperty); }
            set { SetValue(ColumnNameProperty, value); }
        }

        public bool IsFiltered
        {
            get { return (bool)GetValue(IsFilteredProperty); }
            set { SetValue(IsFilteredProperty, value); }
        }

        // 内部字段
        private ObservableCollection<FilterItem> _filterItems;
        private List<string> _distinctValues;

        public FilterControl()
        {
            InitializeComponent();
            _filterItems = new ObservableCollection<FilterItem>();
            _distinctValues = new List<string>();
            ItemsListBox.ItemsSource = _filterItems;
        }

        // 设置筛选项
        public void SetItems(IEnumerable<string> values)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 设置筛选项: {ColumnName}, 值数量: {values?.Count() ?? 0}");

                // 确保ItemsCountText不再显示"加载中..."
                ItemsCountText.Text = "处理中...";

                // 保存传入的数据
                _distinctValues = values?.ToList() ?? new List<string>();
                
                // 清空现有项
                _filterItems.Clear();

                if (_distinctValues.Count == 0)
                {
                    // 如果没有值，添加一个默认项
                    _filterItems.Add(new FilterItem { Text = "无数据", IsSelected = false });
                    ItemsCountText.Text = "无数据";
                    IsFiltered = true;
                    return;
                }

                // 使用批处理添加项以提高UI性能
                const int batchSize = 50;
                for (int i = 0; i < _distinctValues.Count; i += batchSize)
                {
                    int currentBatchSize = Math.Min(batchSize, _distinctValues.Count - i);
                    var batch = _distinctValues.GetRange(i, currentBatchSize);
                    
                    foreach (var value in batch)
                    {
                        _filterItems.Add(new FilterItem { Text = value ?? string.Empty, IsSelected = false });
                    }
                    
                    // 如果数据量大，给UI线程一些时间进行渲染
                    if (_distinctValues.Count > 100)
                    {
                        System.Windows.Application.Current.Dispatcher.Invoke(() => { }, 
                            System.Windows.Threading.DispatcherPriority.Background);
                    }
                }

                ItemsCountText.Text = $"共 {_filterItems.Count} 项";
                IsFiltered = true;

                // 如果有大量项目，折叠ListBox的最大高度以保持UI响应
                ItemsListBox.MaxHeight = Math.Min(300, _filterItems.Count * 25);

                System.Diagnostics.Debug.WriteLine($"[FilterControl] 筛选项设置完成: {ColumnName}, 项数: {_filterItems.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 设置筛选项时出错: {ex.Message}");
                // 确保在出错时也更新文本
                ItemsCountText.Text = "加载错误";
            }
        }

        // 获取选中的值
        public List<string> GetSelectedValues()
        {
            return _filterItems.Where(item => item.IsSelected).Select(item => item.Text).ToList();
        }

        // 按钮点击事件
        private void FilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 筛选按钮点击: {ColumnName}");
                
                // 如果要打开筛选弹窗，先触发预点击事件以更新数据
                if (!FilterPopup.IsOpen)
                {
                    // 触发预点击事件，让外部可以更新筛选数据
                    FilterButton_PreClick?.Invoke(this, EventArgs.Empty);
                    System.Diagnostics.Debug.WriteLine($"[FilterControl] 筛选按钮预点击事件已触发: {ColumnName}");
                }
                
                // 切换弹窗状态
                FilterPopup.IsOpen = !FilterPopup.IsOpen;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 筛选按钮点击时出错: {ex.Message}");
            }
        }

        // 搜索框文本变更事件
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                string searchText = SearchTextBox.Text.ToLower();

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // 不做任何操作，保持当前选择状态
                    return;
                }

                // 根据搜索文本筛选项
                foreach (var item in _filterItems)
                {
                    item.IsSelected = item.Text.ToLower().Contains(searchText);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 搜索时出错: {ex.Message}");
            }
        }

        // 全选按钮点击事件
        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                foreach (var item in _filterItems)
                {
                    item.IsSelected = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 全选时出错: {ex.Message}");
            }
        }

        // 清除按钮点击事件
        private void ClearAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                foreach (var item in _filterItems)
                {
                    item.IsSelected = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 清除时出错: {ex.Message}");
            }
        }

        // 确定按钮点击事件
        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 应用筛选: {ColumnName}");

                // 获取选中的值
                var selectedValues = GetSelectedValues();

                // 更新筛选状态
                IsFiltered = selectedValues.Count < _distinctValues.Count;

                // 触发筛选变更事件
                FilterChanged?.Invoke(this, new FilterEventArgs(ColumnName, selectedValues));

                // 关闭弹出窗口
                FilterPopup.IsOpen = false;

                System.Diagnostics.Debug.WriteLine($"[FilterControl] 筛选应用完成: {ColumnName}, 选中项数: {selectedValues.Count}, 是否筛选: {IsFiltered}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterControl] 应用筛选时出错: {ex.Message}");

                // 确保弹出窗口关闭
                FilterPopup.IsOpen = false;
            }
        }

        // INotifyPropertyChanged 实现
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // 筛选事件参数
    public class FilterEventArgs : EventArgs
    {
        public string ColumnName { get; }
        public List<string> SelectedValues { get; }

        public FilterEventArgs(string columnName, List<string> selectedValues)
        {
            ColumnName = columnName;
            SelectedValues = selectedValues;
        }
    }
}
