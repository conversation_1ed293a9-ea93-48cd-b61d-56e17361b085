<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Tekla.Structures.Catalogs</name>
    </assembly>
    <members>
        <member name="T:Tekla.Structures.Catalogs.BoltItem">
            <summary>
            The BoltItem class contains information about the bolts in the Tekla Structures bolt catalog. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.BoltItem.#ctor">
            <summary>
            Creates a new bolt item instance.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.BoltItem.Standard">
            <summary>
            The bolt item's grade.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.BoltItem.Type">
            <summary>
            The bolt item's type.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.BoltItem.Size">
            <summary>
            The bolt item's size.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.BoltItem.BoltItemTypeEnum">
            <summary>
            Defines the different bolt item types.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.BoltItem.BoltItemTypeEnum.BOLT_UNKNOWN">
            <summary>
            The unknown bolt item type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.BoltItem.BoltItemTypeEnum.BOLT">
            <summary>
            The bolt type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.BoltItem.BoltItemTypeEnum.STUD">
            <summary>
            The stud type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.BoltItemEnumerator">
            <summary>
            The BoltItemEnumerator class allows to loop through the bolt catalog items.
            </summary>
            <example>
            The following example returns true if an item with the standard A325N has been found:
            <code>
            using Tekla.Structures.Catalogs;
            
            public class Example
            {
                   public bool Example1()
                   {
                       bool Result = false;
            
                       CatalogHandler CatalogHandler = new CatalogHandler();
            
                       if (CatalogHandler.GetConnectionStatus())
                       {
                           BoltItemEnumerator BoltItemEnumerator = CatalogHandler.GetBoltItems();
            
                           while (BoltItemEnumerator.MoveNext())
                           {
                               BoltItem BoltItem = BoltItemEnumerator.Current as BoltItem;
            
                               if (BoltItem.Standard == "A325N")
                               {
                                   Result = true;
                                   break;
                               }
                           }
                       }
            
                       return Result;
                   }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Catalogs.BoltItemEnumerator.#ctor">
            <summary>
            The constructor takes as parameter the filter for which items from the catalog
            will enumerate.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.BoltItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.BoltItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.BoltItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.BoltItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the Curret property required by IEnumerator. The returned object
            is always of type ProfileItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.BoltItemEnumerator.Current">
            <summary>
            Returns a bolt item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t">
            <summary>
            Serialization structure for Profile Item Enumerator
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t.MoreBoltItemsLeft">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t.IndexToStart">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t.nBoltItems">
            <summary>
            Number of the profile items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t.aBoltItemNames">
            <summary>
            Bolt grades list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t.aBoltItemTypes">
            <summary>
            Bolt types list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t.aBoltItemSizes">
            <summary>
            Bolt sizes list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t.ClientId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.BoltName">
            <summary>
            The BoltName class contains the name of the bolt item.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.BoltName.Name">
            <summary>
            The bolt item name.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatBoltName_t">
            <summary>
            Serialization structure for ProfileItem.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatBoltName_t.aName">
            <summary>
            The name of the profile.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.CatalogObjectTypeEnum">
            <summary>The catalog object type.</summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.PART">
            <summary>
            The part type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.STEEL_BEAM">
            <summary>
            The steel beam type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.STEEL_COLUMN">
            <summary>
            The steel column type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.STEEL_ORTHOGONAL_BEAM">
            <summary>
            The steel orthogonal beam type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.STEEL_TWIN_PROFILE_BEAM">
            <summary>
            The steel twin profile beam type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.STEEL_CONTOUR_PLATE">
            <summary>
            The steel contour plate type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.STEEL_FOLDED_PLATE">
            <summary>
            The steel folded plate type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.CONCRETE_BEAM">
            <summary>
            The concrete beam type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.CONCRETE_COLUMN">
            <summary>
            The concrete column type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.CONCRETE_PAD_FOOTING">
            <summary>
            The concrete pad footing type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.CONCRETE_STRIP_FOOTING">
            <summary>
            The concrete strip footing type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.CONCRETE_PANEL">
            <summary>
            The concrete panel type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.CONCRETE_SLAB">
            <summary>
            The concrete slab type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.REINFORCING_BAR">
            <summary>
            The reinforcing bar type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.SURFACING">
            <summary>
            The surfacing type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.WELD">
            <summary>
            The weld type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.BOLT">
            <summary>
            The bolt type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.STEEL_ASSEMBLY">
            <summary>
            The steel assembly type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.PRECAST_CONCRETE_ASSEMBLY">
            <summary>
            The precast concrete assembly type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.INSITU_CONCRETE_ASSEMBLY">
            <summary>
            The in situ concrete assembly type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.POUR_OBJECT">
            <summary>
            The pour object type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.POUR_BREAK">
            <summary>
            The pour break type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.GRID">
            <summary>
            The grid type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.PROJECT">
            <summary>
            The project type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.PHASE">
            <summary>
            The phase type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.TASK">
            <summary>
            The task type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.REFERENCE_MODEL">
            <summary>
            The reference model type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.REFERENCE_MODEL_OBJECT">
            <summary>
            The reference model object type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.SINGLE_PART_DRAWING">
            <summary>
            The single part drawing type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.ASSEMBLY_DRAWING">
            <summary>
            The assembly drawing type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.GA_DRAWING">
            <summary>
            The general arrangement drawing type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.MULTI_DRAWING">
            <summary>
            The multidrawing type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.CatalogObjectTypeEnum.CAST_UNIT_DRAWING">
            <summary>
            The cast unit drawing type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.CatalogHandler">
            <summary>
            The CatalogHandler class is a class from which the user can query catalog instances.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.#ctor">
            <summary>
            Creates a new catalog handler instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetConnectionStatus">
            <summary>
            Returns true if a proper connection to the Tekla Structures process has been established.
            If, for some reason, the connection has been lost, the method will return false. Currently,
            there's no way to re-establish the connection.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetProfileItems">
            <summary>
            Returns an enumerator of all profile items.
            </summary>
            <returns>A ProfileItemEnumerator of all profile items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetLibraryProfileItems">
            <summary>
            Returns an enumerator of library profile items.
            </summary>
            <returns>A ProfileItemEnumerator of library profile items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetParametricProfileItems">
            <summary>
            Returns an enumerator of parametric profile items.
            </summary>
            <returns>A ProfileItemEnumerator of parametric profile items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetRebarItems">
            <summary>
            Returns an enumerator of rebar items.
            </summary>
            <returns>A RebarItemEnumerator of rebar items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetMeshItems">
            <summary>
            Returns an enumerator of mesh items.
            </summary>
            <returns>A MeshItemEnumerator of mesh items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetBoltItems">
            <summary>
            Returns an enumerator of all bolt items.
            </summary>
            <returns>A BoltItemEnumerator of all bolt items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetMaterialItems">
            <summary>
            Returns an enumerator of all material items.
            </summary>
            <returns>A MaterialItemEnumerator of all material items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetPrinterItems">
            <summary>
            Returns an enumerator of all printer items.
            </summary>
            <returns>A PrinterItemEnumerator of all printer items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetComponentItems">
            <summary>
            Returns an enumerator of all component items.
            </summary>
            <returns>A ComponentItemEnumerator of all component items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetUserPropertyItems">
            <summary>
            Returns an enumerator of all user property items.
            </summary>
            <returns>A UserPropertyItemEnumerator of all user property items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetUserPropertyItems(Tekla.Structures.Catalogs.CatalogObjectTypeEnum)">
            <summary>
            Returns an enumerator of user property items of the given object type.
            </summary>
            <param name="objectType">The object type to be used.</param>
            <returns>A UserPropertyItemEnumerator of user property items of the given object type.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.CatalogHandler.GetClientId">
            <summary>
            Get unique client id for application.
            Client id must be used with sequential queries, like
            object enumeration.
            </summary>
            <returns>Client id</returns>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ComponentItem">
            <summary>
            The ComponentItem class contains information about the components in the Tekla Structures catalog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ComponentItem.#ctor">
            <summary>
            Creates a new component item instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ComponentItem.Select(System.String,System.Int32)">
            <summary>
            Selects the component item from the component database.
            </summary>
            <param name="Name">The name of the component item to select.</param>
            <param name="Number">The number of the component item to select.</param>
            <returns>True on success.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ComponentItem.UIName">
            <summary>
            The component item's name which is visible in the Tekla Structures user interface.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ComponentItem.Name">
            <summary>
            The component item's internal name which is used by Tekla Structures in component identification.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ComponentItem.Type">
            <summary>
            The component item's type.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ComponentItem.Number">
            <summary>
            The component item's internal number which is used by Tekla Structures in component identification.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ComponentItem.ComponentTypeEnum">
            <summary>
            Defines the different component types.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ComponentItem.ComponentTypeEnum.UNKNOWN">
            <summary>
            The unknown component type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ComponentItem.ComponentTypeEnum.CONNECTION">
            <summary>
            The component type is connection.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ComponentItem.ComponentTypeEnum.COMPONENT">
            <summary>
            The component type is component.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ComponentItem.ComponentTypeEnum.SEAM">
            <summary>
            The component type is seam.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ComponentItem.ComponentTypeEnum.DETAIL">
            <summary>
            The component type is detail.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ComponentItem.ComponentTypeEnum.CUSTOM_PART">
            <summary>
            The component type is custom part object.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ComponentItemEnumerator">
             <summary>
             The ComponentItemEnumerator class allows to loop through the component catalog items.
             </summary>
             <example>
             The following example returns true if an item with the name EndPlate and with the number 144 has been found:
             <code>
             using Tekla.Structures.Catalogs;
             
             public class Example
             {
                    public bool Example1()
                    {
                        bool Result = false;
             
                        CatalogHandler CatalogHandler = new CatalogHandler();
            
                        if (CatalogHandler.GetConnectionStatus())
                        {
                            ComponentItemEnumerator ComponentItemEnumerator = CatalogHandler.GetComponentItems();
            
                            while (ComponentItemEnumerator.MoveNext())
                            {
                                ComponentItem ComponentItem = ComponentItemEnumerator.Current as ComponentItem;
            
                                if (ComponentItem.Name == "EndPlate" &amp;&amp; ComponentItem.Number == 144)
                                {
                                    Result = true;
                                    break;
                                }
                            }
                        }
             
                        return Result;
                    }
             }
             </code>
             </example>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ComponentItemEnumerator.#ctor">
            <summary>
            The constructor takes as parameter the filter for which items from the catalog
            will enumerate.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ComponentItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator.
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ComponentItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ComponentItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ComponentItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the Curret property required by IEnumerator. The returned object
            is always of type ProfileItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ComponentItemEnumerator.Current">
            <summary>
            Returns a component item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t">
            <summary>
            Serialization structure for Profile Item Enumerator
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.MoreComponentItemsLeft">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.IndexToStart">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.nComponentItems">
            <summary>
            Number of the profile items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.aComponentItemNumbers">
            <summary>
            Component numbers list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.aComponentItemTypes">
            <summary>
            Component numbers list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.aComponentItemNames">
            <summary>
            Component names list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.aComponentItemUINames">
            <summary>
            Component names list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t.ClientId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatComponentName_t">
            <summary>
            Serialization structure for ComponentItems name.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatComponentName_t.aName">
            <summary>
            The name of the Component item.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.MaterialItem">
            <summary>
            The MaterialItem class contains information about the materials in the Tekla Structures catalog. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MaterialItem.#ctor">
            <summary>
            Creates a new material item instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MaterialItem.Select(System.String)">
            <summary>
            Selects the material item based on the name from the material database.
            The material name can also be an alias name.
            </summary>
            <param name="materialName">The name of the material.</param>
            <returns>True on success. False if the material was not found.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MaterialItem.MaterialName">
            <summary>
            The material item's name.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MaterialItem.Type">
            <summary>
            The material item's type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.MaterialItem.MaterialItemTypeEnum">
            <summary>
            Defines the different material item types.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.MaterialItem.MaterialItemTypeEnum.MATERIAL_UNKNOWN">
            <summary>
            The unknown material item type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.MaterialItem.MaterialItemTypeEnum.MATERIAL_STEEL">
            <summary>
            The steel material type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.MaterialItem.MaterialItemTypeEnum.MATERIAL_CONCRETE">
            <summary>
            The concrete material type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.MaterialItem.MaterialItemTypeEnum.MATERIAL_TIMBER">
            <summary>
            The timber material type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.MaterialItem.MaterialItemTypeEnum.MATERIAL_MISC">
            <summary>
            The miscellaneous material type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatMaterialItem_t">
            <summary>
            Serialization structure for MaterialItem.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItem_t.MaterialName">
            <summary>
            The name of the material.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItem_t.MaterialType">
            <summary>
            The profile item type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.MaterialItemEnumerator">
            <summary>
            The MaterialItemEnumerator class allows to loop through the catalog material items.
            </summary>
            <example>
            The following example returns true if an item with the name S235JR has been found:
            <code>
            using Tekla.Structures.Catalogs;
            
            public class Example
            {
                   public bool Example1()
                   {
                       bool Result = false;
            
                       CatalogHandler CatalogHandler = new CatalogHandler();
            
                       if (CatalogHandler.GetConnectionStatus())
                       {
                           MaterialItemEnumerator MaterialItemEnumerator = CatalogHandler.GetMaterialItems();
            
                           while (MaterialItemEnumerator.MoveNext())
                           {
                               MaterialItem MaterialItem = MaterialItemEnumerator.Current as MaterialItem;
            
                               if (MaterialItem.MaterialName == "S235JR")
                               {
                                   Result = true;
                                   break;
                               }
                           }
                       }
            
                       return Result;
                   }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MaterialItemEnumerator.#ctor">
            <summary>
            The constructor takes as parameter the filter for which items from the catalog
            will enumerate.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MaterialItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MaterialItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MaterialItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MaterialItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the Curret property required by IEnumerator. The returned object
            is always of type ProfileItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MaterialItemEnumerator.Current">
            <summary>
            Returns a material item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t">
            <summary>
            Serialization structure for Profile Item Enumerator
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t.MoreMaterialItemsLeft">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t.IndexToStart">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t.nMaterialItems">
            <summary>
            Number of the profile items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t.aMaterialItemNames">
            <summary>
            Profile names list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t.aMaterialItemTypes">
            <summary>
            Profile types list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t.ClientId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.MaterialName">
            <summary>
            The MaterialName class contains the name of the material item.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MaterialName.Name">
            <summary>
            The material item name.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatMaterialName_t">
            <summary>
            Serialization structure for ProfileItem.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatMaterialName_t.aName">
            <summary>
            The name of the profile.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.PrinterItem">
            <summary>
            The PrinterItem class contains information about the printers in the Tekla Structures catalog. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.PrinterItem.#ctor">
            <summary>
            Creates a new printer item instance.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.PrinterItem.Name">
            <summary>
            The printer item's name which Tekla Structures uses 
            to fetch all the item's default properties in the printing process.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.PrinterItem.Device">
            <summary>
            The actual printer device used in the printing.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.PrinterItem.Extension">
            <summary>
            The printer item's default file extension which is used when printing to a file.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.PrinterItem.PrintAreaWidth">
            <summary>
            The printer item's print area width.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.PrinterItem.PrintAreaHeigth">
            <summary>
            The printer item's print area height.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.PrinterItemEnumerator">
             <summary>
             The PrinterItemEnumerator class allows to loop through the catalog printer items.
             </summary>
             <example>
             The following example returns true if an item with the name PDFactoryA3 has been found:
             <code>
             using Tekla.Structures.Catalogs;
             
             public class Example
             {
                    public bool Example1()
                    {
                        bool Result = false;
             
                        CatalogHandler CatalogHandler = new CatalogHandler();
            
                        if (CatalogHandler.GetConnectionStatus())
                        {
                            PrinterItemEnumerator PrinterItemEnumerator = CatalogHandler.GetPrinterItems();
            
                            while (PrinterItemEnumerator.MoveNext())
                            {
                                PrinterItem PrinterItem = PrinterItemEnumerator.Current as PrinterItem;
            
                                if (PrinterItem.Name == "PDFactoryA3")
                                {
                                    Result = true;
                                    break;
                                }
                            }
                        }
             
                        return Result;
                    }
             }
             </code>
             </example>
        </member>
        <member name="M:Tekla.Structures.Catalogs.PrinterItemEnumerator.#ctor">
            <summary>
            The constructor takes as parameter the filter for which items from the catalog
            will enumerate.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.PrinterItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.PrinterItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.PrinterItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.PrinterItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the Curret property required by IEnumerator. The returned object
            is always of type ProfileItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.PrinterItemEnumerator.Current">
            <summary>
            Returns a printer item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t">
            <summary>
            Serialization structure for Profile Item Enumerator
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.MorePrinterItemsLeft">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.IndexToStart">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.nPrinterItems">
            <summary>
            Number of the profile items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.aPrinterItemNames">
            <summary>
            Printer names list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.aPrinterItemDevices">
            <summary>
            Printer devices list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.aPrinterItemExtensions">
            <summary>
            Printer extensions list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.aPrinterItemWidths">
            <summary>
            Printer paper area width list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.aPrinterItemHeights">
            <summary>
            Printer paper area heigth list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t.ClientId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatPrinterName_t">
            <summary>
            Serialization structure for PrinterItems name.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterName_t.aName">
            <summary>
            The name of the printer item.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatPrinterDevice_t">
            <summary>
            Serialization structure for PrinterItems device.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterDevice_t.aDevice">
            <summary>
            The device.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatPrinterExtension_t">
            <summary>
            Serialization structure for PrinterItems file extension.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatPrinterExtension_t.aExtension">
            <summary>
            The extension.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.LibraryProfileItem">
            <summary>
            The LibraryProfileItem class contains information from library profiles in
            the catalog. Library profile items can be enumerated using a profile item enumerator.
            </summary>
            <example>
            It is possible to select a library profile item by its name:
            <code>
            using Tekla.Structures.Catalogs;
            
            public class Example
            {
                   public void Example1()
                   {
                       LibraryProfileItem LibraryProfileItem = new LibraryProfileItem();
                       LibraryProfileItem.ProfileName = "HEA300";
                       LibraryProfileItem.Select();
                       //Equivalent code:
                       LibraryProfileItem LibraryProfileItem1 = new LibraryProfileItem();
                       LibraryProfileItem1.Select("HEA300");
                   }
            }
            </code>
            </example>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileItem">
            <summary>
            The ProfileItem abstract class contains the common information of catalog
            profiles (parametric and library).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem._ProfileName">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ProfileItem.IsProfileUserParametric">
            <summary>
            Whether the profile is a parametric user-defined profile. If so,
            the prefix can be asked by type and subtype.
            </summary>
            <returns>Whether the profile is a parametric user-defined profile or not.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ProfileItem.IsProfileUserDefined">
            <summary>
            Whether the profile is a fixed user-defined profile.
            </summary>
            <returns>Whether the profile is a fixed user-defined profile or not.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ProfileItem.Select">
            <summary>
            Selects the profile item in the profile database.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItem.ProfileItemType">
            <summary>
            The profile item type.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubType">
            <summary>
            The profile item subtype.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItem.aProfileItemParameters">
            <summary>
            An array list with the profile item parameters.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItem.Type">
            <summary>
            Whether the profile item is library or parametric.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItem.ParameterString">
            <summary>
            The profile item parameter string.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItem.NumberOfCrossSections">
            <summary>
            The number of cross sections in the profile item.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileItem.TypeEnum">
            <summary>
            Whether the profile is parametric profile or library profile.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum">
            <summary>
            Defines the different profile item types.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.ALL_PROFILES">
            <summary>
            All profiles.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_UNKNOWN">
            <summary>
            The unknown profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_I">
            <summary>
            The I profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_L">
            <summary>
            The L profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_Z">
            <summary>
            The Z profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_U">
            <summary>
            The U profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_PL">
            <summary>
            The plate profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_D">
            <summary>
            The circular section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_PD">
            <summary>
            The circular hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_P">
            <summary>
            The rectangular hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_C">
            <summary>
            The C profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_T">
            <summary>
            The T profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_HK">
            <summary>
            The welded box profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_HQ">
            <summary>
            The HQ profile
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_ZZ">
            <summary>
            The ZZ profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_CC">
            <summary>
            The CC profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_CW">
            <summary>
            The CW profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_CU">
            <summary>
            The CU profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_EB">
            <summary>
            The EB profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_BF">
            <summary>
            The BF profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_SPD">
            <summary>
            The SPD profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_EC">
            <summary>
            The EC profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_ED">
            <summary>
            The ED profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_EE">
            <summary>
            The EE profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_EF">
            <summary>
            The EF profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_EZ">
            <summary>
            The EZ profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_EW">
            <summary>
            The EW profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_POLYGON_PLATE">
            <summary>
            The polygon plate profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_FPL">
            <summary>
            The FPL profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_SP">
            <summary>
            The SP profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_RCDL">
            <summary>
            The RCDL profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_RCXX">
            <summary>
            The RCXX profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_RCL">
            <summary>
            The RCL profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_RCDX">
            <summary>
            The RCDX profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_RCX">
            <summary>
            The RCX profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_USER_DEFINED">
            <summary>
            The user-defined, fixed profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum.PROFILE_USER_PARAMETRIC">
            <summary>
            The user-defined, parametric profile.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum">
            <summary>
            Defines the different profile item subtypes.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_UNKNOWN_SUBTYPE">
            <summary>
            The unknown subtype profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_I_HOT_ROLLED">
            <summary>
            The hot rolled I profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_I_WELDED_SYMMETRICAL">
            <summary>
            The welded symmetrical I profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_I_WELDED_UNSYMMETRICAL">
            <summary>
            The welded unsymmetrical I profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_I_WELDED_SYMMETRICAL2">
            <summary>
            The welded symmetrical, altering height, I profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_I_WELDED_UNSYMMETRICAL2">
            <summary>
            The welded unsymmetrical, altering height, I profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_L_HOT_ROLLED">
            <summary>
            The hot rolled L profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_L_COLD_ROLLED">
            <summary>
            The cold rolled L profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_Z_COLD_ROLLED">
            <summary>
            The cold rolled Z profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_U_HOT_ROLLED">
            <summary>
            The hot rolled U profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_U_COLD_ROLLED">
            <summary>
            The cold rolled U profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_PL_DEFAULT">
            <summary>
            The default plate profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_D_CIRCULAR">
            <summary>
            The default circular section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_D_ELLIPTICAL">
            <summary>
            The elliptical circular section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_PD_CIRCULAR">
            <summary>
            The default circular hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_PD_ELLIPTICAL">
            <summary>
            The elliptical circular hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_PD_CIRCULAR_TAPERED">
            <summary>
            The tapered circular hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_P_SQUARE">
            <summary>
            The square hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_P_RECTANGULAR">
            <summary>
            The rectangular hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_P_ALTERING_HEIGHT">
            <summary>
            The altering height hollow section profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_C_HOT_ROLLED">
            <summary>
            The hot rolled C profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_C_COLD_ROLLED">
            <summary>
            The cold rolled C profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_T_HOT_ROLLED">
            <summary>
            The hot rolled T profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_T_PARAMETRIC">
            <summary>
            The parametric T profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_HK_SYMMETRICAL">
            <summary>
            The symmetrical welded box profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_HK_UNSYMMETRICAL">
            <summary>
            The unsymmetrical welded box profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_HQ_CENTERED">
            <summary>
            The centered HQ profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_HQ_NOT_CENTERED">
            <summary>
            The not centered HQ profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_ZZ_SYMMETRICAL">
            <summary>
            The symmetrical ZZ profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_ZZ_NOT_SYMMETRICAL">
            <summary>
            The unsymmetrical ZZ profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_CC_SYMMETRICAL">
            <summary>
            The symmetrical CC profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_CC_NOT_SYMMETRICAL">
            <summary>
            The unsymmetrical CC profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_CW_SYMMETRICAL">
            <summary>
            The symmetrical CW profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_CW_UNSYMMETRICAL">
            <summary>
            The unsymmetrical CW profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_CU_SYMMETRICAL">
            <summary>
            The symmetrical CU profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_CU_NOT_SYMMETRICAL">
            <summary>
            The unsymmetrical CU profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EB_SYMMETRICAL">
            <summary>
            The symmetrical EB profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EB_NOT_SYMMETRICAL">
            <summary>
            The unsymmetrical EB profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_BF_DEFAULT">
            <summary>
            The default BF profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_SPD_CIRCULAR">
            <summary>
            The circular SPD profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_SPD_ELLIPTICAL">
            <summary>
            The elliptical SPD profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_SPD_CIRCULAR_TAPERED">
            <summary>
            The tapered circular SPD profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EC_SYMMETRICAL">
            <summary>
            The symmetrical EC profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EC_NOT_SYMMETRICAL">
            <summary>
            The unsymmetrical EC profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_ED_DEFAULT">
            <summary>
            The default ED profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EE_DEFAULT">
            <summary>
            The default EE profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EF_DEFAULT">
            <summary>
            The default EF profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EZ_DEFAULT">
            <summary>
            The default EZ profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_EW_DEFAULT">
            <summary>
            The default EW profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCDL_SYMMETRICAL">
            <summary>
            The symmetrical RCDL profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCDL_UNSYMMETRICAL">
            <summary>
            The unsymmetrical RCDL profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCXX_DEFAULT">
            <summary>
            The RCXX default profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCL_DEFAULT">
            <summary>
            The RCL default profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCDX_SYMMETRICAL">
            <summary>
            The symmetrical RCDX profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCDX_UNSYMMETRICAL">
            <summary>
            The unsymmetrical RCDX profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCDX_UNSYMMETRICAL2">
            <summary>
            The unsymmetrical altered height RCDX profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItem.ProfileItemSubTypeEnum.PROFILE_RCX_DEFAULT">
            <summary>
            The RCX default profile.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.LibraryProfileItem.#ctor">
            <summary>
            Creates a new library profile item instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.LibraryProfileItem.Select(System.String)">
            <summary>
            Selects the profile item in the profile database using the given name.
            </summary>
            <param name="profileName">The name of the profile to select.</param>
            <returns>True on success.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.LibraryProfileItem.aProfileItemAnalysisParameters">
            <summary>
            An array list with the profile item analysis parameters.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.LibraryProfileItem.aProfileItemUserParameters">
            <summary>
            An array list with the profile item user parameters.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.LibraryProfileItem.ProfileName">
            <summary>
            The profile item name.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ParametricProfileItem">
            <summary>
            The ParametricProfileItem class contains information from parametric profiles in
            the catalog. Parametric profile items can be enumerated using a profile item enumerator.
            </summary>
            <example>
            It is possible to select a parametric profile item by its name:
            <code>
            using Tekla.Structures.Catalogs;
            
            public class Example
            {
                   public void Example1()
                   {
                       ParametricProfileItem ParametricProfileItem = new ParametricProfileItem();
                       ParametricProfileItem.ProfilePrefix = "PHI";
                       ParametricProfileItem.Select();
                   }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ParametricProfileItem.#ctor">
            <summary>
            Creates a new parametric profile item instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ParametricProfileItem.GetParametricProfilePrefix(System.Int32)">
            <summary>
            Gets the parametric profile item prefix using the given subtype. This can be used
            after retrieving a library profile that has a parametric user-defined type.
            </summary>
            <param name="Subtype">The profile item subtype.</param>
            <returns>The parametric profile item prefix.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ParametricProfileItem.Select(System.String)">
            <summary>
            Selects the parametric profile item in the profile database using the given name.
            </summary>
            <param name="ProfileName">The name of the profile to select.</param>
            <returns>True on success.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ParametricProfileItem.ProfilePrefix">
            <summary>
            The parametric profile item prefix.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatProfileItem_t">
            <summary>
            Serialization structure for ProfileItem.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.ProfileName">
            <summary>
            The name of the profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.Type">
            <summary>
            Whether it is a parametric profile or library profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.ProfileItemType">
            <summary>
            The profile item type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.ProfileItemSubType">
            <summary>
            The profile item subtype.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.aParameterString">
            <summary>
            The parameter string of the profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.nProfileItemParameters">
            <summary>
            Number of profile item parameters.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.aProfileItemParameters">
            <summary>
            Profile item parameters.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.nProfileItemAnalysisParameters">
            <summary>
            Number of profile item parameters.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.aProfileItemAnalysisParameters">
            <summary>
            Profile item parameters.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.nProfileItemUserParameters">
            <summary>
            Number of profile item parameters.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.aProfileItemUserParameters">
            <summary>
            Profile item parameters.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItem_t.NumberOfCrossSections">
            <summary>
            Number of cross sections in profile item.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileItemEnumerator">
            <summary>
            The ProfileItemEnumerator class allows to loop through the catalog profile items.
            </summary>
            <example>
            The following example returns true if a library profile item with the name HEA300 has been found:
            <code>
            using Tekla.Structures.Catalogs;
            
            public class Example
            {
                public bool Example1()
                {
                    bool Result = false;
            
                    CatalogHandler CatalogHandler = new CatalogHandler();
            
                    if (CatalogHandler.GetConnectionStatus())
                    {
                        ProfileItemEnumerator ProfileItemEnumerator = CatalogHandler.GetLibraryProfileItems();
            
                        while (ProfileItemEnumerator.MoveNext())
                        {
                            LibraryProfileItem LibraryProfileItem = ProfileItemEnumerator.Current as LibraryProfileItem;
            
                            if (LibraryProfileItem.ProfileName == "HEA300")
                            {
                                Result = true;
                                break;
                            }
                        }
                    }
            
                    return Result;
                }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ProfileItemEnumerator.#ctor(Tekla.Structures.Catalogs.ProfileItemEnumerator.ProfileItemEnumeratorTypeEnum)">
            <summary>
            The constructor takes as parameter the filter for which items from the catalog
            will enumerate.
            </summary>
            <param name="Type">Profile item type to enumerate</param>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ProfileItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ProfileItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.ProfileItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemEnumerator.SelectInstances">
            <summary>
            Indicates that the instance Select() is called when the 'Current' item is asked from the enumerator.
            The user can set this to 'false' if no members are ever asked from the instance. This is the case
            when, for example, asking only for the available profile names or when only certain profiles need
            to be selected from the model. Without the selection the 'Current' item contains the profile name
            or the prefix and the profile type. Warning: normally the user should not change this value.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the Curret property required by IEnumerator. The returned object
            is always of type ProfileItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemEnumerator.Current">
            <summary>
            Returns a profile item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t">
            <summary>
            Serialization structure for Profile Item Enumerator
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.MoreProfilesItemsLeft">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.IndexToStart">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.nProfileItems">
            <summary>
            Number of the profile items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.ProfileEnumeratorType">
            <summary>
            Tells if it enumerates all the profiles, library or parametric.
            Currently only Library Profiles are supported.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.aProfileItemNames">
            <summary>
            Profile names list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.aProfileItemTypes">
            <summary>
            Profile types list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.aProfileItemProfileTypes">
            <summary>
            Profile types list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t.ClientId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatClientId_t">
            <summary>
            Serialization structure for dotcatClientId_t
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatClientId_t.ProcessId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatClientId_t.ThreadId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileItemParameter">
            <summary>
            The ProfileItemParameter class contains the information of one profile parameter
            (property name, symbol, unit and unit type). A profile item can contain a maximum
            of 50 profile parameters.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemParameter.Property">
            <summary>
            The description of the profile item parameter. Corresponds to the 'Property' in the 
            Tekla Structures profile catalog dialog, and the 'Label in dialog box' in the variable
            dialog in the Sketch Editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemParameter.Value">
            <summary>
            The value of the profile item parameter.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemParameter.StringValue">
            <summary>
            The string value of the profile item parameter.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemParameter.Symbol">
            <summary>
            The symbol of the profile item parameter.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitType">
            <summary>
            Defines the parameter unit type.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileItemParameter.CrossSectionNumber">
            <summary>
            The number of the cross section the parameter belongs to.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum">
            <summary>
            Defines the different unit types of the profile item parameter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_BOOLEAN">
            <summary>
            The input boolean.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_INTEGER">
            <summary>
            The input integer.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_STRING">
            <summary>
            The input string.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_NONE">
            <summary>
            The input none.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_RATIO_UNIT">
            <summary>
            The input ratio unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_STRAIN_UNIT">
            <summary>
            The input strain unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_ANGLE_UNIT">
            <summary>
            The input angle unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_ANGLE_UNIT">
            <summary>
            The output angle unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_SECTION_ANGLE_UNIT">
            <summary>
            The input section angle unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_LENGTH_UNIT">
            <summary>
            The input length unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_LENGTH_UNIT">
            <summary>
            The output length unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DEFORMATION_UNIT">
            <summary>
            The input deformation unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_DEFORMATION_UNIT">
            <summary>
            The output deformation unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DIMENSION_UNIT">
            <summary>
            The input dimension unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_RADIUSOFINERTIA_UNIT">
            <summary>
            The input radius of inertia unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_AREA_UNIT">
            <summary>
            The input area unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_REINFAREA_UNIT">
            <summary>
            The output reinforced area unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_TRANSVREINF_UNIT">
            <summary>
            The output transverse reinforcement unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_AREAPERLENGTH_UNIT">
            <summary>
            The input area per length unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_VOLUME_UNIT">
            <summary>
            The output volume unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_SECTIONMODULUS_UNIT">
            <summary>
            The input section modulus unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_VOLUME_UNIT">
            <summary>
            The input volume unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_MOMENTOFINERTIA_UNIT">
            <summary>
            The input moment of inertia unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_TORSIONCONSTANT_UNIT">
            <summary>
            The input torsion constant unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_WARPINGCONSTANT_UNIT">
            <summary>
            The input warping constant unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_FORCE_UNIT">
            <summary>
            The input force unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_FORCE_UNIT">
            <summary>
            The output force unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_WEIGHT_UNIT">
            <summary>
            The input weight unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_WEIGHT_UNIT">
            <summary>
            The output weight unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DISTRIBLOAD_UNIT">
            <summary>
            The input distributed load unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_DISTRIBLOAD_UNIT">
            <summary>
            The output distributed load unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_SPRINGCONSTANT_UNIT">
            <summary>
            The input spring constant unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_MASSPERLENGTH_UNIT">
            <summary>
            The output mass per length unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_SURFACELOAD_UNIT">
            <summary>
            The input surface load unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_SURFACELOAD_UNIT">
            <summary>
            The output surface load unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_STRENGTH_UNIT">
            <summary>
            The input strength unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_STRESS_UNIT">
            <summary>
            The output stress unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_MODULUS_UNIT">
            <summary>
            The input modulus unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DENSITY_UNIT">
            <summary>
            The input density unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_MOMENT_UNIT">
            <summary>
            The input moment unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_MOMENT_UNIT">
            <summary>
            The output moment unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DISTRIBMOMENT_UNIT">
            <summary>
            The input distributed moment unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_ROTSPRINGCONST_UNIT">
            <summary>
            The input rotation spring constant unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_TEMPERATURE_UNIT">
            <summary>
            The input temperature unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_TEMPERATURE_UNIT">
            <summary>
            The output temperature unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_THERMDILATCOEFF_UNIT">
            <summary>
            The input thermal dilatation coefficient unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_FACTOR_UNIT">
            <summary>
            The input factor unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DATE_UNIT">
            <summary>
            The input date unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DATE_TIME_MIN_UNIT">
            <summary>
            The input date time minutes unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DATE_TIME_SEC_UNIT">
            <summary>
            The input date time seconds unit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_LENGTH_FRACTIONAL_IMPERIAL">
            <summary>
            The input length, fractional imperial.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DEFORMATION_FRACTIONAL_IMPERIAL">
            <summary>
            The input deformation, fractional imperial.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_DIMENSION_FRACTIONAL_IMPERIAL">
            <summary>
            The input dimension, fractional imperial.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.INPUT_RADIUSOFINERTIA_FRACTIONAL_IMPERIAL">
            <summary>
            The radius of inertia, fractional imperial.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_LENGTH_FRACTIONAL_IMPERIAL">
            <summary>
            The output length, fractional imperial.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.ProfileItemParameter.ParameterUnitTypeEnum.OUTPUT_DEFORMATION_FRACTIONAL_IMPERIAL">
            <summary>
            The output deformation, fractional imperial.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatProfileItemParameter_t">
            <summary>
            Serialization structure for Profile Item Parameter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemParameter_t.aProperty">
            <summary>
            The property/description of the parameter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemParameter_t.aSymbol">
            <summary>
            The symbol of the parameter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemParameter_t.Value">
            <summary>
            The value of the parameter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemParameter_t.aStringValue">
            <summary>
            The string value of the parameter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemParameter_t.ParameterUnitType">
            <summary>
            The parameter unit type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileItemParameter_t.CrossSectionNumber">
            <summary>
            The cross section number.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.ProfileName">
            <summary>
            The ProfileName class contains the name of the profile item.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.ProfileName.Name">
            <summary>
            The profile item name.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatProfileName_t">
            <summary>
            Serialization structure for ProfileItem.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatProfileName_t.aName">
            <summary>
            The name of the profile.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.MeshItem">
            <summary>
            The MeshItem class contains information from the meshes in the catalog 
            (mesh_database.inp). 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MeshItem.#ctor">
            <summary>
            Creates a new mesh item instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MeshItem.Select(System.String,System.String)">
            <summary>
            Selects the mesh item in the mesh database.
            </summary>
            <param name="MeshName">The name of the mesh item to select.</param>
            <param name="MeshGrade">The grade of the mesh item to select.</param>
            <returns>True on success.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.Grade">
            <summary>
            The mesh item's grade.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.Name">
            <summary>
            The mesh item's name.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.DiameterLongitudinal">
            <summary>
            The mesh item's longitudinal direction bar size.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.DistanceLongitudinal">
            <summary>
            The mesh item's longitudinal direction distance.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.Length">
            <summary>
            The mesh item's length.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.MinimumOverlappingLongitudinal">
            <summary>
            The mesh item's longitudinal direction minimum overlapping.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.MaximumOverlappingLongitudinal">
            <summary>
            The mesh item's longitudinal direction maximum overlapping.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.LeftOverhangLongitudinal">
            <summary>
            The mesh item's longitudinal direction left overhang.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.RightOverhangLongitudinal">
            <summary>
            The mesh item's longitudinal direction right overhang.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.DiameterCross">
            <summary>
            The mesh item's cross direction bar size.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.DistanceCross">
            <summary>
            The mesh item's cross direction distance.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.Width">
            <summary>
            The mesh item's width.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.MinimumOverlappingCross">
            <summary>
            The mesh item's cross direction minimum overlapping.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.MaximumOverlappingCross">
            <summary>
            The mesh item's cross direction maximum overlapping.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.LeftOverhangCross">
            <summary>
            The mesh item's cross direction left overhang.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItem.RightOverhangCross">
            <summary>
            The mesh item's cross direction right overhang.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.MeshItemEnumerator">
            <summary>
            The MeshItemEnumerator class allows to loop through the catalog mesh items.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MeshItemEnumerator.#ctor">
            <summary>
            Creates a new instance of the MeshItemEnumerator.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MeshItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MeshItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MeshItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.MeshItemEnumerator.SelectMeshItem(System.String,System.String)">
            <summary>
            Selects a mesh item in the mesh database with the given name and grade.
            </summary>
            <param name="Name">The name of the mesh item to select.</param>
            <param name="Grade">The grade of the mesh item to select.</param>
            <returns>The first MeshItem that matches the conditions.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the CurrenFt property required by IEnumerator. The returned object
            is always of type MeshItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.MeshItemEnumerator.Current">
            <summary>
            Returns a mesh item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t">
            <summary>
            The MeshItemEnumerator_t structure.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.IndexToStart">
            <summary>
            The index to start from.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.MeshItems">
            <summary>
            The number of mesh items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.Grade">
            <summary>
            The mesh grade list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.Name">
            <summary>
            The mesh name list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.DiameterLongitudinal">
            <summary>
            The mesh longitudinal bar size list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.DistanceLongitudinal">
            <summary>
            The mesh longitudinal pitch list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.Length">
            <summary>
            The mesh longitudinal mesh size (Length) list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.MinimumOverlappingLongitudinal">
            <summary>
            The mesh longitudinal minimum overlapping list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.MaximumOverlappingLongitudinal">
            <summary>
            The mesh longitudinal maximum overlapping list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.LeftOverhangLongitudinal">
            <summary>
            The mesh longitudinal left overhang list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.RightOverhangLongitudinal">
            <summary>
            The mesh longitudinal right overhang list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.DiameterCross">
            <summary>
            The mesh cross bar size list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.DistanceCross">
            <summary>
            The mesh cross pitch list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.Width">
            <summary>
            The mesh cross mesh size (Width) list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.MinimumOverlappingCross">
            <summary>
            The mesh cross minimum overlapping list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.MaximumOverlappingCross">
            <summary>
            The mesh cross maximum overlapping list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.LeftOverhangCross">
            <summary>
            The mesh cross left overhang list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.MeshItemEnumerator_t.RightOverhangCross">
            <summary>
            The mesh cross right overhang list.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.Parser">
            <summary>
            Class to parse the inp file containing the rebars
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.Parser.Parse(System.String,Tekla.Structures.CatalogInternal.RebarItemEnumerator_t@)">
            <summary>
               Parses specified file to get rebars from the catalog file.
            </summary>
            <param name="FilePath"> File to parse. </param>
            <param name="RebarList"> RebarItemEnumerator_t. </param>
        </member>
        <member name="M:Tekla.Structures.Catalogs.Parser.Parse(System.String,Tekla.Structures.CatalogInternal.MeshItemEnumerator_t@)">
            <summary>
               Parses specified file to get meshes from the catalog file.
            </summary>
            <param name="FilePath"> File to parse. </param>
            <param name="MeshList"> MeshItemEnumerator_t. </param>
        </member>
        <member name="M:Tekla.Structures.Catalogs.Parser.GetCatalogPath(System.String)">
            <summary>
            Gets the path for a certain catalog file.
            </summary>
            <param name="FileName">File name for the catalog to find</param>
            <returns>The paths where the catalog file is located.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.Parser.LineToArrayOfStrings(System.String,System.Collections.Generic.List{System.String}@)">
            <summary>
            Splits a string using space as a separator. Everything between quotes 
            is considered to be a one string.
            </summary>
            <param name="Line">The line to split.</param>
            <param name="Parameters">The list of obtained substrings.</param>
        </member>
        <member name="T:Tekla.Structures.Catalogs.RebarItem">
            <summary>
            The RebarItem class contains information from the rebars in the catalog 
            (rebar_database.inp). 
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.RebarItem.MAIN_USAGE">
            <summary>
            The main usage string.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.RebarItem.TIE_STIRRUP_USAGE">
            <summary>
            The tie/stirrup usage string.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItem.#ctor">
            <summary>
            Creates a new rebar item instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItem.Select(System.String,System.String,System.Double)">
            <summary>
            Selects the rebar item in the rebar database.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Size">The size of the rebar item to select.</param>
            <param name="BendRadius">The bending radius of the rebar item to select.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItem.Select(System.String,System.Double,System.Double,System.Boolean)">
            <summary>
            Selects the rebar item in the rebar database.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Diameter">The diameter of the rebar item to select.</param>
            <param name="BendRadius">The bending radius of the rebar item to select.</param>
            <param name="UseNominalDiameter">
            If set to false, the item is selected based on the actual diameter.
            If true, the item is selected based on the nominal diameter.
            </param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItem.Select(System.String,System.String,System.String)">
            <summary>
            Selects the rebar item in the rebar database.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Size">The size of the rebar item to select.</param>
            <param name="Usage">The usage of the rebar item to select.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItem.Select(System.String,System.Double,System.String,System.Boolean)">
            <summary>
            Selects the rebar item in the rebar database.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Diameter">The diameter of the rebar item to select.</param>
            <param name="Usage">The usage of the rebar item to select.</param>
            <param name="UseNominalDiameter">
            If set to false, the item is selected based on the actual diameter.
            If true, the item is selected based on the nominal diameter.
            </param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItem.Select(System.String,System.String)">
            <summary>
            Selects the rebar item in the rebar database.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Size">The size of the rebar item to select.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItem.Select(System.String,System.Double,System.Boolean)">
            <summary>
            Selects the rebar item in the rebar database.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="NominalDiameter">The diameter of the rebar item to select.</param>
            <param name="UseNominalDiameter">
            If set to false, the item is selected based on the actual diameter.
            If true, the item is selected based on the nominal diameter.
            </param>
            <returns>True on success.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.Grade">
            <summary>
            The rebar item's grade.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.Size">
            <summary>
            The rebar item's size.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.Usage">
            <summary>
            The rebar item's usage.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.BendRadius">
            <summary>
            The rebar item's bending radius.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.Code">
            <summary>
            The rebar item's code.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.NominalDiameter">
            <summary>
            The rebar item's nominal diameter.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.ActualDiameter">
            <summary>
            The rebar item's actual diameter.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.WeightPerLenght">
            <summary>
            The rebar item's weight per lenght.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.CrossSectionArea">
            <summary>
            The rebar item's cross section area.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.HookRadius90Degrees">
            <summary>
            The rebar item's hook radius for 90 degrees.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.HookLength90Degrees">
            <summary>
            The rebar item's hook length for 90 degrees.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.HookRadius135Degrees">
            <summary>
            The rebar item's hook radius for 135 degrees.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.HookLength135Degrees">
            <summary>
            The rebar item's hook length for 135 degrees.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.HookRadius180Degrees">
            <summary>
            The rebar item's hook radius for 180 degrees.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItem.HookLength180Degrees">
            <summary>
            The rebar item's hook length for 180 degrees.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.RebarItemEnumerator">
            <summary>
            The RebarItemEnumerator class allows to loop through the catalog rebar items.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.#ctor">
            <summary>
            Creates a new instance of the RebarItemEnumerator.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.SelectRebarItem(System.String,System.String,System.Double)">
            <summary>
            Selects a rebar item in the rebar database with the given grade, size and bending radius.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Size">The size of the rebar item to select.</param>
            <param name="BendRadius">The bending radius of the rebar item to select.</param>
            <returns>The first RebarItem that matches the conditions.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.SelectRebarItem(System.String,System.String,System.String)">
            <summary>
            Selects a rebar item in the rebar database with the given grade, size and bending radius.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Size">The size of the rebar item to select.</param>
            <param name="Usage">The usage of the rebar item to select.</param>
            <returns>The first RebarItem that matches the conditions.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.SelectRebarItem(System.String,System.String)">
            <summary>
            Selects a rebar item in the rebar database with the given grade and size.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Size">The size of the rebar item to select.</param>
            <returns>The first RebarItem that matches the conditions.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.SelectRebarItem(System.String,System.Double,System.Double,System.Boolean)">
            <summary>
            Selects a rebar item in the rebar database with the given grade, nominal diameter and bending radius.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Diameter">The diameter of the rebar item to select.</param>
            <param name="BendRadius">The bending radius of the rebar item to select.</param>
            <param name="UseNominalDiameter">
            If set to false, the item is selected based on the actual diameter.
            If true, the item is selected based on the nominal diameter.
            </param>
            <returns>The first RebarItem that matches the conditions.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.SelectRebarItem(System.String,System.Double,System.String,System.Boolean)">
            <summary>
            Selects a rebar item in the rebar database with the given grade, nominal diameter and bending radius.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Diameter">The diameter of the rebar item to select.</param>
            <param name="Usage">The usage of the rebar item to select.</param>
            <param name="UseNominalDiameter">
            If set to false, the item is selected based on the actual diameter.
            If true, the item is selected based on the nominal diameter.
            </param>
            <returns>The first RebarItem that matches the conditions.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.RebarItemEnumerator.SelectRebarItem(System.String,System.Double,System.Boolean)">
            <summary>
            Selects a rebar item in the rebar database with the given grade and nominal diameter.
            </summary>
            <param name="Grade">The grade of the rebar item to select.</param>
            <param name="Diameter">The diameter of the rebar item to select.</param>
            <param name="UseNominalDiameter">
            If set to false, the item is selected based on the actual diameter.
            If true, the item is selected based on the nominal diameter.
            </param>
            <returns>The first RebarItem that matches the conditions.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the Current property required by IEnumerator. The returned object
            is always of type RebarItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.RebarItemEnumerator.Current">
            <summary>
            Returns a rebar item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t">
            <summary>
            The RebarItemEnumerator_t structure.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.IndexToStart">
            <summary>
            The index to start from.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.RebarItems">
            <summary>
            The number of rebar items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.Size">
            <summary>
            The rebar size list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.Grade">
            <summary>
            The rebar grade list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.Usage">
            <summary>
            The rebar usage list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.BendRadius">
            <summary>
            The rebar bend radius list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.Code">
            <summary>
            The rebar code list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.NominalDiameter">
            <summary>
            The rebar nominal diameter list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.ActualDiameter">
            <summary>
            The rebar actual diameter list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.WeightPerLenght">
            <summary>
            The rebar weight per lenght list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.CrossSectionArea">
            <summary>
            The rebar cross section area list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.HookRadius90Degrees">
            <summary>
            The rebar hook radius at 90 degrees list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.HookLength90Degrees">
            <summary>
            The rebar hook length at 90 degrees list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.HookRadius135Degrees">
            <summary>
            The rebar hook radius at 135 degrees list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.HookLength135Degrees">
            <summary>
            The rebar hook length at 135 degrees list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.HookRadius180Degrees">
            <summary>
            The rebar hook radius at 180 degrees list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.RebarItemEnumerator_t.HookLength180Degrees">
            <summary>
            The rebar hook length at 180 degrees list.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.CDelegateSynchronized">
            <summary>
            Provides akit synchronization to CCatalogDelegate.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.ICDelegate">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportEnumerateProfiles(Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportSelectProfileItem(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportGetParametricProfilePrefixFromSubtype(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportEnumerateMaterials(Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportSelectMaterialItem(Tekla.Structures.CatalogInternal.dotcatMaterialItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportEnumerateBolts(Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pBoltEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportEnumeratePrinters(Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pPrinterEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportEnumerateComponents(Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pComponentEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportEnumerateUserProperties(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportInsertUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportModifyUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportDeleteUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportSelectUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportSelectUserPropertyItemObjectTypes(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItemObjectTypes"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportUserPropertyItemObjectTypeOperation(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItemObjectTypeOperation"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.ICDelegate.ExportGetModelPath(System.String@)">
            <summary>
            Get path to the currently open model's directory
            </summary>
            <param name="Path">The returned path</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.#ctor">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.BeginInvoke(System.Delegate,System.Object[])">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="method"></param>
            <param name="args"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.EndInvoke(System.IAsyncResult)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.Invoke(System.Delegate,System.Object[])">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="method"></param>
            <param name="args"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t@,System.IAsyncResult)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pEnumerator"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@,System.IAsyncResult)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t@,System.IAsyncResult)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pEnumerator"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t@,System.IAsyncResult)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pEnumerator"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t@,System.IAsyncResult)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pEnumerator"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportEnumerateProfiles(Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportSelectProfileItem(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportGetParametricProfilePrefixFromSubtype(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportEnumerateMaterials(Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportSelectMaterialItem(Tekla.Structures.CatalogInternal.dotcatMaterialItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportEnumerateBolts(Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pBoltEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportEnumeratePrinters(Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pPrinterEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportEnumerateComponents(Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pComponentEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportEnumerateUserProperties(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportInsertUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportModifyUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportDeleteUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportSelectUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportSelectUserPropertyItemObjectTypes(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItemObjectTypes"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportUserPropertyItemObjectTypeOperation(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItemObjectTypeOperation"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.ExportGetModelPath(System.String@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportEnumerateProfiles(Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportSelectProfileItem(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportGetParametricProfilePrefixFromSubtype(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportEnumerateMaterials(Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportSelectMaterialItem(Tekla.Structures.CatalogInternal.dotcatMaterialItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportEnumerateBolts(Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pBoltEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportEnumeratePrinters(Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pPrinterEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportEnumerateComponents(Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pComponentEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportEnumerateUserProperties(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportInsertUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportModifyUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportDeleteUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportSelectUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportSelectUserPropertyItemObjectTypes(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItemObjectTypes"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotcatExportUserPropertyItemObjectTypeOperation(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pObjectTypeUserPropertyItemOperation"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateSynchronized.dotdiaGetModelPath(System.Text.StringBuilder)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="Path"></param>
            <returns></returns>
        </member>
        <member name="P:Tekla.Structures.CatalogInternal.CDelegateSynchronized.InvokeRequired">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.CDelegateWrapper">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.CDelegateWrapper._instance">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.CDelegateWrapper._functionality">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.#ctor(Tekla.Structures.CatalogInternal.ICDelegate,Tekla.Structures.Internal.WrapperFunctionalityBase)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="instance"></param>
            <param name="functionality"></param>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportEnumerateProfiles(Tekla.Structures.CatalogInternal.dotcatProfileItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportSelectProfileItem(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportGetParametricProfilePrefixFromSubtype(Tekla.Structures.CatalogInternal.dotcatProfileItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pProfileItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportEnumerateMaterials(Tekla.Structures.CatalogInternal.dotcatMaterialItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportSelectMaterialItem(Tekla.Structures.CatalogInternal.dotcatMaterialItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pMaterialItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportEnumerateBolts(Tekla.Structures.CatalogInternal.dotcatBoltItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pBoltEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportEnumeratePrinters(Tekla.Structures.CatalogInternal.dotcatPrinterItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pPrinterEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportEnumerateComponents(Tekla.Structures.CatalogInternal.dotcatComponentItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pComponentEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportEnumerateUserProperties(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyEnumerator"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportInsertUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportModifyUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportDeleteUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportSelectUserPropertyItem(Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItem"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportSelectUserPropertyItemObjectTypes(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItemObjectTypes"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportUserPropertyItemObjectTypeOperation(Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pUserPropertyItemObjectTypeOperation"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegateWrapper.ExportGetModelPath(System.String@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="Path"></param>
            <returns></returns>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.CDelegate">
            <summary>
            Interface from catalog assembly to TS
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegate.#ctor">
            <summary>
            Instantiates a new CDelegate
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.CDelegate.Singletons">
            <summary>
            Storage class for singleton instances.
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.CDelegate.Singletons.#cctor">
            <summary>
            Explicit static constructor to tell C# compiler
            not to mark type as beforefieldinit. Do not remove.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.DelegateProxy">
            <summary>
            CDelegate remote class proxy.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.DelegateProxy._Instance">
            <summary>
            The remote delegate object.
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.DelegateProxy.#cctor">
            <summary>
            Initializes static instance variable.
            </summary>
        </member>
        <member name="P:Tekla.Structures.CatalogInternal.DelegateProxy.Delegate">
            <summary>
            Gets the singleton CDelegate instance that includes model API methods.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.Remoter">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.Remoter.ChannelName">
            <summary>
            Name of the remoting channel to register.
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.Remoter.PublishTypes">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.Remoter.InitializeSandBox">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <returns></returns>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.DotNetProxy">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.CatalogInternal.DotNetProxy.Run(System.String)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="Param">Not Used.</param>
            <returns>The return value is not usefull because AKIT doesn't send it to Core.</returns>
        </member>
        <member name="T:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum">
            <summary>The user property field type.</summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_UNDEFINED">
            <summary>
            The user property field type is undefined.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_NUMBER">
            <summary>
            The user property field type is number.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_TEXT">
            <summary>
            The user property field type is text.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DISTANCE">
            <summary>
            The user property field type is distance.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_PROFILE">
            <summary>
            The user property field type is profile.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_MATERIAL">
            <summary>
            The user property field type is material.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_TEXT_LIST_DISTANCE">
            <summary>
            The user property field type is distance list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_FILE_IN">
            <summary>
            The user property field type is file in.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_FILE_OUT">
            <summary>
            The user property field type is file out.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_BOLT_STANDARD">
            <summary>
            The user property field type is bolt standard.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_BOLT_SIZE">
            <summary>
            The user property field type is bolt size.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_RATIO">
            <summary>
            The user property field type is ratio.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_STRAIN">
            <summary>
            The user property field type is strain.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_ANGLE">
            <summary>
            The user property field type is angle.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DEFORMATION">
            <summary>
            The user property field type is deformation.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DIMENSION">
            <summary>
            The user property field type is dimension.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_RADIUSOFINERTIA">
            <summary>
            The user property field type is radius of inertia.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_AREA">
            <summary>
            The user property field type is area.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_AREAPERLENGTH">
            <summary>
            The user property field type is area/length.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_SECTIONMODULUS">
            <summary>
            The user property field type is section modulus.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_MOMENTOFINERTIA">
            <summary>
            The user property field type is moment of inertia.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_TORSIONCONSTANT">
            <summary>
            The user property field type is torsion constant.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_WARPINGCONSTANT">
            <summary>
            The user property field type is warping constant.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_FORCE">
            <summary>
            The user property field type is force.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_WEIGHT">
            <summary>
            The user property field type is weight.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DISTRIBLOAD">
            <summary>
            The user property field type is distributed load.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_SPRINGCONSTANT">
            <summary>
            The user property field type is spring constant.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_SURFACELOAD">
            <summary>
            The user property field type is surface load.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_STRENGTH">
            <summary>
            The user property field type is strength.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_MODULUS">
            <summary>
            The user property field type is modulus.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DENSITY">
            <summary>
            The user property field type is density.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_MOMENT">
            <summary>
            The user property field type is moment.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DISTRIBMOMENT">
            <summary>
            The user property field type is distributed moment.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_ROTSPRINGCONST">
            <summary>
            The user property field type is rotational spring constant.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_TEMPERATURE">
            <summary>
            The user property field type is temperature.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_THERMDILATCOEFF">
            <summary>
            The user property field type is thermal coefficient.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_ANALYSIS_RESTRAINT">
            <summary>
            The user property field type is analysis restraint.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_VOLUME">
            <summary>
            The user property field type is volume.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_REBAR_MAIN">
            <summary>
            The user property field type is main reinforcement bar.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_REBAR_STIRRUP">
            <summary>
            The user property field type is stirrup reinforcement bar.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DATE">
            <summary>
            The user property field type is date.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DATE_TIME_SEC">
            <summary>
            The user property field type is date and time with seconds.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_DATE_TIME_MIN">
            <summary>
            The user property field type is date and time with minutes.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_STUD_STANDARD">
            <summary>
            The user property field type is stud standard.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_STUD_SIZE">
            <summary>
            The user property field type is stud size.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_STUD_LENGTH">
            <summary>
            The user property field type is stud length.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_HOLE_TYPE">
            <summary>
            The user property field type is hole type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_HOLE_DIRECTION">
            <summary>
            The user property field type is hole direction.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_WELD_TYPE">
            <summary>
            The user property field type is weld type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_CHAMFER_TYPE">
            <summary>
            The user property field type is chamfer type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_WELDING_SITE">
            <summary>
            The user property field type is welding site.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_FACTOR">
            <summary>
            The user property field type is factor.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_PART_NAME">
            <summary>
            The user property field type is part name.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_BOLT_TYPE">
            <summary>
            The user property field type is bolt type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_COMPONENT_NAME">
            <summary>
            The user property field type is component name.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_REBAR_MESH">
            <summary>
            The user property field type is rebar mesh.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_USERDEFINED">
            <summary>
            The user property field type is user defined.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_YES_NO">
            <summary>
            The user property field type is yes/no.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_COMPONENT_STANDARD_FILE">
            <summary>
            The user property field type is component standard file.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_REBAR_GRADE">
            <summary>
            The user property field type is reinforcement bar grade.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_REBAR_RADIUS">
            <summary>
            The user property field type is reinforcement bar radius.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_REBAR_SIZE">
            <summary>
            The user property field type is reinforcement bar size.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_HOOK_SHAPE">
            <summary>
            The user property field type is reinforcement bar hook shape.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyFieldTypeEnum.FIELDTYPE_CROSSBAR_POSITION">
            <summary>
            The user property field type is reinforcement cross bar position.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.UserPropertyLevelEnum">
            <summary>The user property level.</summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyLevelEnum.LEVEL_MODEL">
            <summary>
            The user property has been defined as a model user property.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyLevelEnum.LEVEL_PROJECT">
            <summary>
            The user property has been defined as a project user property.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyLevelEnum.LEVEL_FIRM">
            <summary>
            The user property has been defined as a firm user property.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyLevelEnum.LEVEL_ENVIRONMENT">
            <summary>
            The user property has been defined as an environment user property.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyLevelEnum.LEVEL_COMMONDEFAULT">
            <summary>
            The user property has been defined as a common default user property.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.UserPropertyVisibilityEnum">
            <summary>The visibility of the user property.</summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyVisibilityEnum.VISIBILITY_NORMAL">
            <summary>
            The user property is visible and the user can modify it.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyVisibilityEnum.VISIBILITY_READONLY">
            <summary>
            The user property is visible but the user cannot modify it.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Catalogs.UserPropertyVisibilityEnum.VISIBILITY_HIDDEN">
            <summary>
            The user property is hidden.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.UserPropertyItem">
            <summary>
            The UserPropertyItem class contains information about the user properties in the Tekla Structures catalog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.#ctor">
            <summary>
            Creates a new user property item instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetDefaultValue(System.Int32@)">
            <summary>
            Gets the default value of an integer property.
            </summary>
            <param name="DefaultValue">The value that was gotten.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SetDefaultValue(System.Int32)">
            <summary>
            Sets the default value of an integer property.
            </summary>
            <param name="DefaultValue">The new value.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetDefaultValue(System.Double@)">
            <summary>
            Gets the default value of a double property.
            </summary>
            <param name="DefaultValue">The value that was gotten.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SetDefaultValue(System.Double)">
            <summary>
            Sets the default value of a double property.
            </summary>
            <param name="DefaultValue">The new value.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetDefaultValue(System.String@)">
            <summary>
            Gets the default value of a string property.
            </summary>
            <param name="DefaultValue">The value that was gotten.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SetDefaultValue(System.String)">
            <summary>
            Sets the default value of a string property.
            </summary>
            <param name="DefaultValue">The new value.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetOptions(System.Collections.Generic.List{System.Collections.Generic.KeyValuePair{System.Int32,System.String}}@)">
            <summary>
            Gets the value options of an integer property.
            </summary>
            <param name="IntOptions">The value options that were gotten.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SetOptions(System.Collections.Generic.List{System.Collections.Generic.KeyValuePair{System.Int32,System.String}})">
            <summary>
            Sets the value options of an integer property.
            </summary>
            <param name="IntOptions">The new value options.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetOptions(System.Collections.Generic.List{System.Collections.Generic.KeyValuePair{System.Double,System.String}}@)">
            <summary>
            Gets the value options of a double property.
            </summary>
            <param name="DoubleOptions">The value options that were gotten.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SetOptions(System.Collections.Generic.List{System.Collections.Generic.KeyValuePair{System.Double,System.String}})">
            <summary>
            Sets the value options of a double property.
            </summary>
            <param name="DoubleOptions">The new value options.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetOptions(System.Collections.Generic.List{System.Collections.Generic.KeyValuePair{System.String,System.String}}@)">
            <summary>
            Gets the value options of a string property.
            </summary>
            <param name="StringOptions">The value options that were gotten.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SetOptions(System.Collections.Generic.List{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Sets the value options of a string property.
            </summary>
            <param name="StringOptions">The new value options.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SetLabel(System.String)">
            <summary>
            Sets the label of the user property.
            </summary>
            <param name="label">The new label.</param>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetLabel">
            <summary>
            Gets the label of the user property.
            </summary>
            <returns>The label of the user property.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.Insert">
            <summary>
            Inserts the user property item to the database.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.Modify">
            <summary>
            Modifies the user property item.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.Delete">
            <summary>
            Deletes the user property item from the database.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.Select">
            <summary>
            Selects by name the user property item from the database.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.GetObjectTypes(System.Collections.Generic.List{Tekla.Structures.Catalogs.CatalogObjectTypeEnum}@)">
            <summary>
            Gets the object types which contain this user property item.
            </summary>
            <param name="objectTypes">The object types that were gotten.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.AddToObjectType(Tekla.Structures.Catalogs.CatalogObjectTypeEnum)">
            <summary>
            Adds the user property item to the given object type.
            </summary>
            <param name="objectType">The object type to add the item to.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.RemoveFromObjectType(Tekla.Structures.Catalogs.CatalogObjectTypeEnum)">
            <summary>
            Removes the user property item from the given object type.
            </summary>
            <param name="objectType">The object type to remove the item from.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.InsertProperty">
            <summary>
            Inserts user property item to the database.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.ModifyProperty">
            <summary>
            Modifies user property item.
            Only model level properties can be modified.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.DeleteProperty">
            <summary>
            Deletes the user property item from the database.
            Only model level properties can be deleted.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItem.SelectProperty">
            <summary>
            Selects the user property item from the database by name.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItem.Name">
            <summary>
            The name of the user property.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItem.Type">
            <summary>
            The type of the user property.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItem.FieldType">
            <summary>
            The field type of the user property.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItem.Level">
            <summary>
            The level at which the user property has been defined.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItem.AffectsNumbering">
            <summary>
            Indicates whether the property value affects the numbering of objects.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItem.Unique">
            <summary>
            Indicates whether the property value is copied when the object is copied.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItem.Visibility">
            <summary>
            Indicates whether the property value is visible/editable.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t">
            <summary>
            Serialization structure for UserPropertyItem.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.Name">
            <summary>
            The name of the user property
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.Label">
            <summary>
            The label of the user property
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.Type">
            <summary>
            The type of the user property.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.FieldType">
            <summary>
            The field type of the user property.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.Level">
            <summary>
            The level of the user property.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.AffectsNumbering">
            <summary>
            AffectsNumbering.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.Unique">
            <summary>
            Unique.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.Visibility">
            <summary>
            Visibility.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.IntDefaultValue">
            <summary>
            Integer default value.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.DoubleDefaultValue">
            <summary>
            Double default value.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.StringDefaultValue">
            <summary>
            String default value.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.nOptionValues">
            <summary>
            Count of option values.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItem_t.aOptions">
            <summary>
            Option values.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t">
            <summary>
            Serialization structure for UserPropertyItemObjectTypes.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t.Name">
            <summary>
            The name of the user property
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t.nObjectTypes">
            <summary>
            The count of the object types
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypes_t.aObjectTypes">
            <summary>
            Object types.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t">
            <summary>
            Serialization structure for UserPropertyItemObjectTypeOp.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t.Name">
            <summary>
            The name of the user property
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t.ObjectType">
            <summary>
            The object type
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemObjectTypeOperation_t.Operation">
            <summary>
            The operation
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.UserPropertyItemEnumerator">
            <summary>
            The UserPropertyItemEnumerator class allows to loop through the user property items.
            </summary>
            <example>
            The following example returns true if an item with the name "comment" has been found:
            <code>
            using Tekla.Structures.Catalogs;
            
            public class Example
            {
                   public bool Example1()
                   {
                       bool Result = false;
            
                       CatalogHandler CatalogHandler = new CatalogHandler();
            
                       if (CatalogHandler.GetConnectionStatus())
                       {
                           UserPropertyItemEnumerator UserPropertyItemEnumerator = CatalogHandler.GetUserPropertyItems();
            
                           while (UserPropertyItemEnumerator.MoveNext())
                           {
                               UserPropertyItem UserPropertyItem = UserPropertyItemEnumerator.Current as UserPropertyItem;
            
                               if (UserPropertyItem.Name == "comment")
                               {
                                   Result = true;
                                   break;
                               }
                           }
                       }
            
                       return Result;
                   }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItemEnumerator.#ctor">
            <summary>
            Enumerator for all user property items.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItemEnumerator.#ctor(Tekla.Structures.Catalogs.CatalogObjectTypeEnum)">
            <summary>
            Enumerator for user property items of object type.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItemEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator.
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItemEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Catalogs.UserPropertyItemEnumerator.GetSize">
            <summary>
            Returns the total amout of items.
            </summary>
            <returns>The total amount of items.</returns>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItemEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Implementation for the Curret property required by IEnumerator. The returned object
            is always of type ProfileItem.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyItemEnumerator.Current">
            <summary>
            Returns a user property item instance of the current element.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t">
            <summary>
            Serialization structure for Profile Item Enumerator
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t.ObjectType">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t.MoreUserPropertyItemsLeft">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t.IndexToStart">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t.nUserPropertyItems">
            <summary>
            Number of the user property items in the enumerator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t.aUserPropertyItemNames">
            <summary>
            User property names list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t.aUserPropertyItemTypes">
            <summary>
            User Property types list.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyItemEnumerator_t.ClientId">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.UserPropertyName">
            <summary>
            The UserPropertyName class contains the name of the user property item.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyName.Name">
            <summary>
            The user property item name.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatUserPropertyName_t">
            <summary>
            Serialization structure for UserPropertyName.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyName_t.aName">
            <summary>
            The name of the property.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Catalogs.UserPropertyOption">
            <summary>
            The UserPropertyOption class contains the properties of a user property value option.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyOption.IntValue">
            <summary>
            The integer value.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyOption.DoubleValue">
            <summary>
            The double value.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyOption.StringValue">
            <summary>
            The string value.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Catalogs.UserPropertyOption.OptionLabel">
            <summary>
            The option name.
            </summary>
        </member>
        <member name="T:Tekla.Structures.CatalogInternal.dotcatUserPropertyOption_t">
            <summary>
            Serialization structure for UserPropertyOption.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyOption_t.IntValue">
            <summary>
            Integer option value.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyOption_t.DoubleValue">
            <summary>
            Double option value.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyOption_t.StringValue">
            <summary>
            String option value.
            </summary>
        </member>
        <member name="F:Tekla.Structures.CatalogInternal.dotcatUserPropertyOption_t.OptionLabel">
            <summary>
            Option name.
            </summary>
        </member>
    </members>
</doc>
