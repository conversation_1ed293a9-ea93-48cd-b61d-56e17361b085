﻿<Application x:Class="TeklaTool.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:TeklaTool"
             xmlns:utils="clr-namespace:TeklaTool.Utils"
             xmlns:converters="clr-namespace:TeklaTool.Converters"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <utils:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <utils:BoolToInverseVisibilityConverter x:Key="BoolToInverseVisibilityConverter"/>
            <utils:BoolToModeTextConverter x:Key="BoolToModeTextConverter"/>
            <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>

            <Style TargetType="Button">
                <Setter Property="Padding" Value="8,3"/>
                <Setter Property="Margin" Value="2"/>
            </Style>

            <Style TargetType="TextBlock">
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>

            <Style TargetType="DataGrid">
                <Setter Property="AutoGenerateColumns" Value="False"/>
                <Setter Property="CanUserAddRows" Value="False"/>
                <Setter Property="CanUserDeleteRows" Value="False"/>
                <Setter Property="CanUserReorderColumns" Value="True"/>
                <Setter Property="CanUserResizeColumns" Value="True"/>
                <Setter Property="CanUserSortColumns" Value="True"/>
                <Setter Property="AlternatingRowBackground" Value="AliceBlue"/>
                <Setter Property="GridLinesVisibility" Value="All"/>
                <Setter Property="HeadersVisibility" Value="All"/>
                <Setter Property="SelectionMode" Value="Extended"/>
                <Setter Property="SelectionUnit" Value="FullRow"/>
                <Setter Property="VirtualizingPanel.IsVirtualizing" Value="True"/>
                <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Recycling"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
