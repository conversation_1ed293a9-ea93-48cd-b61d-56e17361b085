using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TeklaTool.Models
{
    /// <summary>
    /// 构件信息类，用于存储构件的信息
    /// </summary>
    public class AssemblyInfo : INotifyPropertyChanged
    {
        private int _index;
        private string _assemblyNumber = string.Empty;
        private string _name = string.Empty;
        private string _profile = string.Empty;
        private string _material = string.Empty;
        private string _finish = string.Empty;
        private string _class = string.Empty;
        private string _phase = string.Empty;
        private int _count = 1;
        private int _partCount = 0;
        private string _assemblyPrefix = string.Empty;
        private string _assemblyStartNumber = string.Empty;
        private string _guid = string.Empty;
        private int _modelObjectId;
        private string _remark = string.Empty;
        private ObservableCollection<string> _allAssemblyNumbers = new ObservableCollection<string>();

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 索引
        /// </summary>
        public int Index
        {
            get => _index;
            set
            {
                if (_index != value)
                {
                    _index = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 构件编号
        /// </summary>
        public string AssemblyNumber
        {
            get => _assemblyNumber;
            set
            {
                if (_assemblyNumber != value)
                {
                    _assemblyNumber = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 名称
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 截面
        /// </summary>
        public string Profile
        {
            get => _profile;
            set
            {
                if (_profile != value)
                {
                    _profile = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 材质
        /// </summary>
        public string Material
        {
            get => _material;
            set
            {
                if (_material != value)
                {
                    _material = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 表面处理
        /// </summary>
        public string Finish
        {
            get => _finish;
            set
            {
                if (_finish != value)
                {
                    _finish = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 等级
        /// </summary>
        public string Class
        {
            get => _class;
            set
            {
                if (_class != value)
                {
                    _class = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 阶段
        /// </summary>
        public string Phase
        {
            get => _phase;
            set
            {
                if (_phase != value)
                {
                    _phase = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 数量
        /// </summary>
        public int Count
        {
            get => _count;
            set
            {
                if (_count != value)
                {
                    _count = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 零件数量
        /// </summary>
        public int PartCount
        {
            get => _partCount;
            set
            {
                if (_partCount != value)
                {
                    _partCount = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 构件前缀
        /// </summary>
        public string AssemblyPrefix
        {
            get => _assemblyPrefix;
            set
            {
                if (_assemblyPrefix != value)
                {
                    _assemblyPrefix = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 构件起始编号
        /// </summary>
        public string AssemblyStartNumber
        {
            get => _assemblyStartNumber;
            set
            {
                if (_assemblyStartNumber != value)
                {
                    _assemblyStartNumber = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// GUID
        /// </summary>
        public string Guid
        {
            get => _guid;
            set
            {
                if (_guid != value)
                {
                    _guid = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 模型对象ID
        /// </summary>
        public int ModelObjectId
        {
            get => _modelObjectId;
            set
            {
                if (_modelObjectId != value)
                {
                    _modelObjectId = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark
        {
            get => _remark;
            set
            {
                if (_remark != value)
                {
                    _remark = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// 所有构件编号，用于选择
        /// </summary>
        public ObservableCollection<string> AllAssemblyNumbers
        {
            get => _allAssemblyNumbers;
            set
            {
                if (_allAssemblyNumbers != value)
                {
                    _allAssemblyNumbers = value;
                    OnPropertyChanged();
                }
            }
        }
    }
}
