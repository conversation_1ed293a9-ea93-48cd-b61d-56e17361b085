using System;
using System.Windows;
using Tekla.Structures.Model;

namespace TeklaTool.Utils
{
    /// <summary>
    /// 异常处理工具类
    /// </summary>
    public static class ExceptionHandler
    {
        /// <summary>
        /// 处理Tekla相关异常
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <param name="operationName">操作名称</param>
        /// <returns>操作是否成功</returns>
        public static bool HandleTeklaOperation(Action action, string operationName)
        {
            try
            {
                action?.Invoke();
                return true;
            }
            catch (ModelException mex)
            {
                Logger.LogError($"Tekla模型操作失败 [{operationName}]: {mex.Message}");
                ShowUserError($"Tekla模型操作失败: {mex.Message}", "模型错误");
                return false;
            }
            catch (UnauthorizedAccessException uex)
            {
                Logger.LogError($"权限不足 [{operationName}]: {uex.Message}");
                ShowUserError("操作权限不足，请检查Tekla Structures是否正常运行", "权限错误");
                return false;
            }
            catch (InvalidOperationException iex)
            {
                Logger.LogWarning($"无效操作 [{operationName}]: {iex.Message}");
                ShowUserWarning($"操作无效: {iex.Message}", "操作警告");
                return false;
            }
            catch (TimeoutException tex)
            {
                Logger.LogError($"操作超时 [{operationName}]: {tex.Message}");
                ShowUserError("操作超时，请稍后重试", "超时错误");
                return false;
            }
            catch (OutOfMemoryException omex)
            {
                Logger.LogError($"内存不足 [{operationName}]: {omex.Message}");
                ShowUserError("内存不足，请关闭其他程序后重试", "内存错误");
                return false;
            }
            catch (Exception ex)
            {
                Logger.LogError($"未预期的错误 [{operationName}]: {ex}");
                ShowUserError($"发生未预期的错误: {ex.Message}\n\n请联系技术支持", "系统错误");
                return false;
            }
        }

        /// <summary>
        /// 处理Tekla相关异步操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="defaultValue">默认返回值</param>
        /// <returns>操作结果</returns>
        public static T HandleTeklaOperation<T>(Func<T> func, string operationName, T defaultValue = default(T))
        {
            try
            {
                return func != null ? func() : defaultValue;
            }
            catch (ModelException mex)
            {
                Logger.LogError($"Tekla模型操作失败 [{operationName}]: {mex.Message}");
                ShowUserError($"Tekla模型操作失败: {mex.Message}", "模型错误");
                return defaultValue;
            }
            catch (UnauthorizedAccessException uex)
            {
                Logger.LogError($"权限不足 [{operationName}]: {uex.Message}");
                ShowUserError("操作权限不足，请检查Tekla Structures是否正常运行", "权限错误");
                return defaultValue;
            }
            catch (InvalidOperationException iex)
            {
                Logger.LogWarning($"无效操作 [{operationName}]: {iex.Message}");
                ShowUserWarning($"操作无效: {iex.Message}", "操作警告");
                return defaultValue;
            }
            catch (TimeoutException tex)
            {
                Logger.LogError($"操作超时 [{operationName}]: {tex.Message}");
                ShowUserError("操作超时，请稍后重试", "超时错误");
                return defaultValue;
            }
            catch (OutOfMemoryException omex)
            {
                Logger.LogError($"内存不足 [{operationName}]: {omex.Message}");
                ShowUserError("内存不足，请关闭其他程序后重试", "内存错误");
                return defaultValue;
            }
            catch (Exception ex)
            {
                Logger.LogError($"未预期的错误 [{operationName}]: {ex}");
                ShowUserError($"发生未预期的错误: {ex.Message}\n\n请联系技术支持", "系统错误");
                return defaultValue;
            }
        }

        /// <summary>
        /// 显示错误消息给用户
        /// </summary>
        private static void ShowUserError(string message, string title)
        {
            Application.Current?.Dispatcher?.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            });
        }

        /// <summary>
        /// 显示警告消息给用户
        /// </summary>
        private static void ShowUserWarning(string message, string title)
        {
            Application.Current?.Dispatcher?.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            });
        }

        /// <summary>
        /// 记录并忽略异常（用于非关键操作）
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <param name="operationName">操作名称</param>
        public static void SafeExecute(Action action, string operationName)
        {
            try
            {
                action?.Invoke();
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"非关键操作失败 [{operationName}]: {ex.Message}");
                // 不显示用户消息，只记录日志
            }
        }
    }
}