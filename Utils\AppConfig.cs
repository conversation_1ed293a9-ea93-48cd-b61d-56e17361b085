using System;

namespace TeklaTool.Utils
{
    /// <summary>
    /// 应用程序配置类
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 缓存有效期
        /// </summary>
        public TimeSpan CacheDuration { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// 最大缓存大小
        /// </summary>
        public int MaxCacheSize { get; set; } = 10000;

        /// <summary>
        /// 是否启用调试日志
        /// </summary>
        public bool EnableDebugLogging { get; set; } = false;

        /// <summary>
        /// 数据加载超时时间（秒）
        /// </summary>
        public int LoadTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 是否启用数据缓存
        /// </summary>
        public bool EnableDataCache { get; set; } = true;

        /// <summary>
        /// 最大并发操作数
        /// </summary>
        public int MaxConcurrentOperations { get; set; } = 4;

        /// <summary>
        /// 日志文件保留天数
        /// </summary>
        public int LogRetentionDays { get; set; } = 7;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static AppConfig Instance { get; } = new AppConfig();

        private AppConfig() { }
    }
}