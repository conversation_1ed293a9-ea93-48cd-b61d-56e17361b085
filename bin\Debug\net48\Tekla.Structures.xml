<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Tekla.Structures</name>
    </assembly>
    <members>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Events">
            <summary>The Events class allows the user to register event listeners for Tekla Structures events.</summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Events.OnTeklaStructuresExit(System.String,System.Object[])">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Events.InitializeLifetimeService">
            <summary>
            Initializes the lifetime service.
            </summary>
            <returns>Always null.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Events.#ctor">
            <summary>
            Creates an empty events instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Events.Register">
            <summary>
            Registers the instance to listen to the specified events.
            More event delegates should not be added without calling UnRegister
            first.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Events.UnRegister">
            <summary>
            Unregisters the instance from listening to events.
            </summary>
        </member>
        <member name="E:Tekla.Structures.TeklaStructuresInternal.Events.TeklaStructuresExit">
            <summary>
            The TeklaStructuresExit event is raised just before Tekla Structures exits. After this event has been called 
            the user will no longer be able to perform any calls to Tekla Structures.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Events.TeklaStructuresExitDelegate">
            <summary>
            The delegate to use for Tekla Structures exit.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.BinaryFilterExpression">
            <summary>
            The BinaryFilterExpression class represents a binary expression between two filter expressions.
            This class cannot be inherited.
            </summary>
            <seealso cref="T:Tekla.Structures.Filtering.BinaryFilterExpressionCollection"/>
            <example>
            The following example creates this BinaryFilterExpression:
            
            PartName == BEAM1
            
            <code>
            using System;
            using Tekla.Structures.Filtering;
            using Tekla.Structures.Filtering.Categories;
            
            public class FilterExample
            {
                   // Creates a binary filter expression:
                   // PartName == BEAM1
                   public BinaryFilterExpression CreateBinaryFilterExpression()
                   {
                       // Creates the filter expressions
                       PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                       StringConstantFilterExpression Beam1 = new StringConstantFilterExpression("BEAM1");
            
                       // Creates the binary filter expression
                       return new BinaryFilterExpression(PartName, StringOperatorType.IS_EQUAL, Beam1);
                   }
            }
            </code>
            </example>
        </member>
        <member name="T:Tekla.Structures.Filtering.FilterExpression">
            <summary>
            The FilterExpression class represents a filter expression. This is a base class for other
            filter expressions and should not be used directly.
            </summary>
            <seealso cref="T:Tekla.Structures.Filtering.BinaryFilterExpression"/>
            <seealso cref="T:Tekla.Structures.Filtering.BinaryFilterExpressionCollection"/>
        </member>
        <member name="T:Tekla.Structures.Filtering.Expression">
            <summary>
            The Expression class represents an expression. This is a base class for other expressions and cannot be used directly.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.Expression._ExpressionId">
            <summary>
            Internal Expression Identifier.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Expression.Clone``1(``0)">
            <summary>
            Clones an object.
            </summary>
            <typeparam name="T">The object type.</typeparam>
            <param name="Source">The object to be used.</param>
            <returns>The cloned object.</returns>
            <exception cref="T:System.ArgumentException">Thrown when a non-serializable object is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.Expression.ToString">
            <summary>
            Creates a string representation of the current object.
            </summary>
            <returns>A new string representing the current object.</returns>
        </member>
        <member name="P:Tekla.Structures.Filtering.Expression.Properties">
            <summary>
            Gets or sets the dictionary of custom properties.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.FilterExpression.IsEnable">
            <summary>
            Gets or sets the enabled state of the filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpression.#ctor(Tekla.Structures.Filtering.DataFilterExpression,Tekla.Structures.Filtering.OperatorType,Tekla.Structures.Filtering.DataFilterExpression)">
            <summary>
            Initializes a new instance of the BinaryFilterExpression class.
            </summary>
            <param name="Left">The left operand of the filter expression.</param>
            <param name="Oper">The operator of the filter expression.</param>
            <param name="Right">The right operand of the filter expression.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null filter expression is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpression.#ctor(Tekla.Structures.Filtering.BooleanFilterExpression,Tekla.Structures.Filtering.BooleanOperatorType,Tekla.Structures.Filtering.BooleanConstantFilterExpression)">
            <summary>
            Initializes a new instance of the BinaryFilterExpression class.
            </summary>
            <param name="Left">The left operand of the filter expression.</param>
            <param name="Oper">The operator of the filter expression.</param>
            <param name="Right">The right operand of the filter expression.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null filter expression is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpression.#ctor(Tekla.Structures.Filtering.StringFilterExpression,Tekla.Structures.Filtering.StringOperatorType,Tekla.Structures.Filtering.StringConstantFilterExpression)">
            <summary>
            Initializes a new instance of the BinaryFilterExpression class.
            </summary>
            <param name="Left">The left operand of the filter expression.</param>
            <param name="Oper">The operator of the filter expression.</param>
            <param name="Right">The right operand of the filter expression.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null filter expression is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpression.#ctor(Tekla.Structures.Filtering.NumericFilterExpression,Tekla.Structures.Filtering.NumericOperatorType,Tekla.Structures.Filtering.NumericConstantFilterExpression)">
            <summary>
            Initializes a new instance of the BinaryFilterExpression class.
            </summary>
            <param name="Left">The left operand of the filter expression.</param>
            <param name="Oper">The operator of the filter expression.</param>
            <param name="Right">The right operand of the filter expression.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null filter expression is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpression.#ctor(Tekla.Structures.Filtering.DateTimeFilterExpression,Tekla.Structures.Filtering.DateTimeOperatorType,Tekla.Structures.Filtering.DateTimeConstantFilterExpression)">
            <summary>
            Initializes a new instance of the BinaryFilterExpression class.
            </summary>
            <param name="Left">The left operand of the filter expression.</param>
            <param name="Oper">The operator of the filter expression.</param>
            <param name="Right">The right operand of the filter expression.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null filter expression is specified.</exception>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpression.Left">
            <summary>
            Gets or sets the left operand of the filter expression.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpression.Operator">
            <summary>
            Gets or sets the operator of the filter expression.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpression.Right">
            <summary>
            Gets or sets the right operand of the filter expression.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.BinaryFilterExpressionCollection">
             <summary>
             The BinaryFilterExpressionCollection class represents a collection of BinaryFilterItem objects.
             The class is used to represent linear expressions. This class cannot be inherited.
             </summary>
             <example>
             The following examples show how to compose different filter expression collections.
             After the BinaryFilterExpressionCollection is composed use <see cref="T:Tekla.Structures.Filtering.Filter"/>
             to build the filter file.
             <code>
             using System;
             using Tekla.Structures.Filtering;
             using Tekla.Structures.Filtering.Categories;
             
             public class FilterExamples
             {
                    // Creates a filter for the following filter expression:
                    // (PartName == BEAM1 OR PartName == BEAM2 OR PartName == BEAM3)
                    public BinaryFilterExpressionCollection CreateBinaryFilterExpressionCollection()
                    {
                        // Creates the filter expressions
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression Beam1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression Beam2 = new StringConstantFilterExpression("BEAM2");
                        StringConstantFilterExpression Beam3 = new StringConstantFilterExpression("BEAM3");
             
                        // Creates the binary filter expressions
                        BinaryFilterExpression Expression1 = new BinaryFilterExpression(PartName, StringOperatorType.IS_EQUAL, Beam1);
                        BinaryFilterExpression Expression2 = new BinaryFilterExpression(PartName, StringOperatorType.IS_EQUAL, Beam2);
                        BinaryFilterExpression Expression3 = new BinaryFilterExpression(PartName, StringOperatorType.IS_EQUAL, Beam3);
             
                        // Creates the binary filter expression collection
                        BinaryFilterExpressionCollection ExpressionCollection = new BinaryFilterExpressionCollection();
                        ExpressionCollection.Add(new BinaryFilterExpressionItem(Expression1, BinaryFilterOperatorType.BOOLEAN_OR));
                        ExpressionCollection.Add(new BinaryFilterExpressionItem(Expression2, BinaryFilterOperatorType.BOOLEAN_OR));
                        ExpressionCollection.Add(new BinaryFilterExpressionItem(Expression3));
                        return ExpressionCollection;
                    }
            
                    // Creates the following expression:
                    // (PartName != BEAM AND (PartName != BEAM1 AND PartName != BEAM2))
                    public static FilterExpression CreateBinaryFilterExpressionCollection1()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
            
                        BinaryFilterExpression A = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression C = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
                        BinaryFilterExpression D = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(C, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(D));
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection1 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(A, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection));
            
                        return BinaryFilterExpressionCollection1;
                    }
            
                    // Creates the following expression:
                    // (PartName != BEAM AND PartName != BEAM1 AND (PartName != BEAM2 OR PartName != BEAM3))
                    public static FilterExpression CreateBinaryFilterExpressionCollection2()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
                        StringConstantFilterExpression BeamName3 = new StringConstantFilterExpression("BEAM3");
            
                        BinaryFilterExpression A = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression B = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
                        BinaryFilterExpression C = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
                        BinaryFilterExpression D = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName3);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(C, BinaryFilterOperatorType.BOOLEAN_OR));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(D));
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection1 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(A, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(B, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection));
                        return BinaryFilterExpressionCollection1;
                    }
            
                    // Creates the following expression:
                    // (PartName != BEAM AND (PartName != BEAM2 OR PartName != BEAM3) AND PartName != BEAM1)
                    public static FilterExpression CreateBinaryFilterExpressionCollection3()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
                        StringConstantFilterExpression BeamName3 = new StringConstantFilterExpression("BEAM3");
            
                        BinaryFilterExpression A = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression B = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
                        BinaryFilterExpression C = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
                        BinaryFilterExpression D = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName3);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(C, BinaryFilterOperatorType.BOOLEAN_OR));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(D));
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection1 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(A, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection,
                            BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(B));
            
                        return BinaryFilterExpressionCollection1;
                    }
             
                    // Creates the following expression:
                    // ((PartName != BEAM AND PartName != BEAM1 AND PartName != BEAM2 OR PartName != BEAM3) AND
                    // (PartName != BEAM4 AND PartName != BEAM5 AND PartName != BEAM6 OR PartName != BEAM7))
                    public static FilterExpression CreateBinaryFilterExpressionCollection4()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
                        StringConstantFilterExpression BeamName3 = new StringConstantFilterExpression("BEAM3");
                        StringConstantFilterExpression BeamName4 = new StringConstantFilterExpression("BEAM4");
                        StringConstantFilterExpression BeamName5 = new StringConstantFilterExpression("BEAM5");
                        StringConstantFilterExpression BeamName6 = new StringConstantFilterExpression("BEAM6");
                        StringConstantFilterExpression BeamName7 = new StringConstantFilterExpression("BEAM7");
            
                        BinaryFilterExpression A = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression B = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
                        BinaryFilterExpression C = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
                        BinaryFilterExpression D = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName3);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(A, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(B, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(C, BinaryFilterOperatorType.BOOLEAN_OR));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(D));
            
                        BinaryFilterExpression E = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName4);
                        BinaryFilterExpression F = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName5);
                        BinaryFilterExpression G = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName6);
                        BinaryFilterExpression H = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName7);
             
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection1 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(E, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(F, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(G, BinaryFilterOperatorType.BOOLEAN_OR));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(H));
             
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection2 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection2.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection,
                            BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection2.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection1));
            
                        return BinaryFilterExpressionCollection2;
                    }
            
                    // Creates the following expression:
                    // ((PartName != BEAM AND PartName != BEAM1) AND
                    // (PartName != BEAM2 AND PartName != BEAM3) AND
                    // (PartName != BEAM4 AND PartName != BEAM5))
                    public static FilterExpression CreateBinaryFilterExpressionCollection5()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
                        StringConstantFilterExpression BeamName3 = new StringConstantFilterExpression("BEAM3");
                        StringConstantFilterExpression BeamName4 = new StringConstantFilterExpression("BEAM4");
                        StringConstantFilterExpression BeamName5 = new StringConstantFilterExpression("BEAM5");
            
                        BinaryFilterExpression A = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression B = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(A, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(B));
            
                        BinaryFilterExpression C = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
                        BinaryFilterExpression D = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName3);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection1 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(C, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(D));
            
                        BinaryFilterExpression E = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName4);
                        BinaryFilterExpression F = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName5);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection2 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection2.Add(new BinaryFilterExpressionItem(E, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection2.Add(new BinaryFilterExpressionItem(F));
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection3 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection3.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection,
                            BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection3.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection1,
                            BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection3.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection2));
            
                        return BinaryFilterExpressionCollection3;
                    }
            
                    // Creates the following expression:
                    // (((PartName != BEAM AND PartName != BEAM1) AND
                    // (PartName != BEAM2 AND PartName != BEAM3) AND
                    // (PartName != BEAM4 AND PartName != BEAM5)) OR
                    // (PartName != BEAM6 AND PartName != BEAM7))
                    public static FilterExpression CreateBinaryFilterExpressionCollection6()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
                        StringConstantFilterExpression BeamName3 = new StringConstantFilterExpression("BEAM3");
                        StringConstantFilterExpression BeamName4 = new StringConstantFilterExpression("BEAM4");
                        StringConstantFilterExpression BeamName5 = new StringConstantFilterExpression("BEAM5");
                        StringConstantFilterExpression BeamName6 = new StringConstantFilterExpression("BEAM6");
                        StringConstantFilterExpression BeamName7 = new StringConstantFilterExpression("BEAM7");
            
                        BinaryFilterExpression A = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression B = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(A, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection.Add(new BinaryFilterExpressionItem(B));
            
                        BinaryFilterExpression C = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
                        BinaryFilterExpression D = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName3);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection1 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(C, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection1.Add(new BinaryFilterExpressionItem(D));
            
                        BinaryFilterExpression E = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName4);
                        BinaryFilterExpression F = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName5);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection2 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection2.Add(new BinaryFilterExpressionItem(E, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection2.Add(new BinaryFilterExpressionItem(F));
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection3 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection3.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection,
                            BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection3.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection1,
                            BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection3.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection2));
            
                        BinaryFilterExpression G = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName6);
                        BinaryFilterExpression H = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName7);
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection4 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection4.Add(new BinaryFilterExpressionItem(G, BinaryFilterOperatorType.BOOLEAN_AND));
                        BinaryFilterExpressionCollection4.Add(new BinaryFilterExpressionItem(H));
            
                        BinaryFilterExpressionCollection BinaryFilterExpressionCollection5 = new BinaryFilterExpressionCollection();
                        BinaryFilterExpressionCollection5.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection3,
                            BinaryFilterOperatorType.BOOLEAN_OR));
                        BinaryFilterExpressionCollection5.Add(new BinaryFilterExpressionItem(BinaryFilterExpressionCollection4));
            
                        return BinaryFilterExpressionCollection5;
                    }
            
                    // Creates the following expression:
                    // (PartName != BEAM OR (PartName != BEAM1 OR (PartName != BEAM2)))
                    public static FilterExpression CreateBinaryFilterExpressionCollection7()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
            
                        BinaryFilterExpression X = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression Y = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
                        BinaryFilterExpression Z = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
            
                        BinaryFilterExpressionCollection C2 = new BinaryFilterExpressionCollection();
                        C2.Add(new BinaryFilterExpressionItem(Z));
            
                        BinaryFilterExpressionCollection C1 = new BinaryFilterExpressionCollection();
                        C1.Add(new BinaryFilterExpressionItem(Y, BinaryFilterOperatorType.BOOLEAN_OR));
                        C1.Add(new BinaryFilterExpressionItem(C2));
            
                        BinaryFilterExpressionCollection C0 = new BinaryFilterExpressionCollection();
                        C0.Add(new BinaryFilterExpressionItem(X, BinaryFilterOperatorType.BOOLEAN_OR));
                        C0.Add(new BinaryFilterExpressionItem(C1));
             
                        return C0;
                    }
            
                    // Creates the following expression:
                    // (((PartName != BEAM) OR PartName != BEAM1) OR PartName != BEAM2)
                    public static FilterExpression CreateBinaryFilterExpressionCollection8()
                    {
                        PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                        StringConstantFilterExpression BeamName = new StringConstantFilterExpression("BEAM");
                        StringConstantFilterExpression BeamName1 = new StringConstantFilterExpression("BEAM1");
                        StringConstantFilterExpression BeamName2 = new StringConstantFilterExpression("BEAM2");
            
                        BinaryFilterExpression X = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName);
                        BinaryFilterExpression Y = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName1);
                        BinaryFilterExpression Z = new BinaryFilterExpression(PartName, StringOperatorType.IS_NOT_EQUAL, BeamName2);
            
                        BinaryFilterExpressionCollection C0 = new BinaryFilterExpressionCollection();
                        C0.Add(new BinaryFilterExpressionItem(X));
                        BinaryFilterExpressionCollection C1 = new BinaryFilterExpressionCollection();
                        C1.Add(new BinaryFilterExpressionItem(C0, BinaryFilterOperatorType.BOOLEAN_OR));
                        C1.Add(new BinaryFilterExpressionItem(Y));
                        BinaryFilterExpressionCollection C2 = new BinaryFilterExpressionCollection();
                        C2.Add(new BinaryFilterExpressionItem(C1, BinaryFilterOperatorType.BOOLEAN_OR));
                        C2.Add(new BinaryFilterExpressionItem(Z));
                        return C2;
                    }
             }
             </code>
             </example>
        </member>
        <member name="F:Tekla.Structures.Filtering.BinaryFilterExpressionCollection._Items">
            <summary>
            Data container.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.#ctor">
            <summary>
            Initializes a new instance of the BinaryFilterExpressionCollection class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.IndexOf(Tekla.Structures.Filtering.BinaryFilterExpressionItem)">
            <summary>
            Searches for the specified BinaryFilterExpressionItem in the collection and returns a zero-based index if found.
            </summary>
            <param name="Item">The item to search for. This value cannot be null.</param>
            <returns>The zero-based index of the item in the collection. -1 if not found.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied item is null.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.Add(Tekla.Structures.Filtering.BinaryFilterExpressionItem)">
            <summary>
            Adds a BinaryFilterExpressionItem object at the end of the collection.
            </summary>
            <param name="Item">The object to add to the collection. This value cannot be null.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied item is null.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.Insert(System.Int32,Tekla.Structures.Filtering.BinaryFilterExpressionItem)">
            <summary>
            Inserts an element in the collection at a specified index.
            </summary>
            <param name="Index">The zero-based index at which the item shoud be inserted.</param>
            <param name="Item">The item to insert. This value cannot be null.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied item is null.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the supplied index is out of bounds.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.Remove(Tekla.Structures.Filtering.BinaryFilterExpressionItem)">
            <summary>
            Removes the first occurrence of a specific object from the collection.
            </summary>
            <param name="Item">The object to remove from the collection. This value cannot be null.</param>
            <returns>True if item was successfully removed. False otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the element at the specified index from the collection.
            </summary>
            <param name="Index">The index of the item to remove.</param>
            <exception cref="T:System.ArgumentException">Thrown when the supplied index is out of bounds.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.Clear">
            <summary>
            Removes all the elements from the collection.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.Contains(Tekla.Structures.Filtering.BinaryFilterExpressionItem)">
            <summary>
            Determines whether an element is in the collection.
            </summary>
            <param name="Item">The object to locate in the collection. This value cannot be null.</param>
            <returns>True if the object exists.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied item is null.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.CopyTo(Tekla.Structures.Filtering.BinaryFilterExpressionItem[],System.Int32)">
            <summary>
            Copies the collection to the input array.
            </summary>
            <param name="Array">The input array. This value cannot be null.</param>
            <param name="ArrayIndex">The starting index.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied item is null.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the supplied array index is out of bounds.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.IsFirst(Tekla.Structures.Filtering.BinaryFilterExpressionItem)">
            <summary>
            Determines whether an element is the first item of the collection.
            </summary>
            <param name="BinaryFilterExpressionItem">The element to evaluate. This value cannot be null.</param>
            <returns>True if the element is the first item.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied item is null.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.IsLast(Tekla.Structures.Filtering.BinaryFilterExpressionItem)">
            <summary>
            Determines whether an element is the last item of the collection.
            </summary>
            <param name="BinaryFilterExpressionItem">The element to evaluate. This value cannot be null.</param>
            <returns>True if the element is the last item.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied item is null.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.GetFirst">
            <summary>
            Gets the first item of the collection.
            </summary>
            <returns>The first item of the collection.</returns>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.GetLast">
            <summary>
            Gets the last item of the collection.
            </summary>
            <returns>The last item of the collection.</returns>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.IsSynchronized">
            <summary>
            Gets a value indicating whether the collection supports multithreading.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.SyncRoot">
            <summary>
            Gets the root for synchronization.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.Count">
            <summary>
            Gets the number of elements actually contained in the collection.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.IsReadOnly">
            <summary>
            Gets a value indicating whether the collection is read-only.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.Item(System.Int32)">
            <summary>
            Gets or sets the item at a specific index.
            </summary>
            <param name="Index">The item index.</param>
            <returns>The item at the index.</returns>
        </member>
        <member name="T:Tekla.Structures.Filtering.BinaryFilterExpressionCollection.BinaryFilterExpressionEnumerator">
            <summary>
            Support class for BinaryFilterExpressionCollection.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.BinaryFilterExpressionItem">
            <summary>
            The BinaryFilterExpressionItem class represents a data item in a <see cref="T:Tekla.Structures.Filtering.BinaryFilterExpressionCollection"/>.
            This class cannot be inherited.
            </summary>
            <seealso cref="T:Tekla.Structures.Filtering.BinaryFilterExpression"/>
            <seealso cref="T:Tekla.Structures.Filtering.BinaryFilterExpressionCollection"/>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionItem.#ctor(Tekla.Structures.Filtering.FilterExpression)">
            <summary>
            Initializes a new instance of the BinaryFilterExpressionItem class.
            </summary>
            <param name="FilterExpression">The filter expression to collect.</param>
            <exception cref="T:Tekla.Structures.Filtering.InvalidFilterExpressionException">Thrown when an invalid filter expression is specified.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when a null filter expression is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.BinaryFilterExpressionItem.#ctor(Tekla.Structures.Filtering.FilterExpression,Tekla.Structures.Filtering.BinaryFilterOperatorType)">
            <summary>
            Initializes a new instance of the BinaryFilterExpressionItem class.
            </summary>
            <param name="FilterExpression">The <see cref="P:Tekla.Structures.Filtering.BinaryFilterExpressionItem.FilterExpression"/> object to collect.</param>
            <param name="BinaryFilterItemOperatorType">The operator type to be used against the next item.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null filter expression is specified.</exception>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpressionItem.FilterExpression">
            <summary>
            Gets or sets the FilterExpression object.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.BinaryFilterExpressionItem.BinaryFilterItemOperatorType">
            <summary>
            Gets or sets the BinaryFilterItemOperatorType.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.BinaryFilterOperatorType">
            <summary>
            The binary filter operator type defines the operators between two binary filters.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.BinaryFilterOperatorType.BOOLEAN_OR">
            <summary>
            The Boolean OR operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.BinaryFilterOperatorType.BOOLEAN_AND">
            <summary>
            The Boolean AND operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.BinaryFilterOperatorType.EMPTY">
            <summary>
            The empty operator represents the end of the expression.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.BooleanConstantFilterExpression">
            <summary>
            The BooleanConstantFilterExpression class represents a constant Boolean filter expression.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.DataFilterExpression">
            <summary>
            The DataFilterExpression class represents a basic data type for a filter expression.
            This is a base class for other filter expressions and cannot be used directly.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.DataFilterExpression.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the DataFilterExpression class.
            </summary>
            <param name="Value">The object to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.DataFilterExpression.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the DataFilterExpression class.
            </summary>
            <param name="Name">The name of the expression.</param>
            <param name="Category">The category of the expression.</param>
            <param name="Property">The property of the expression.</param>
            <param name="LocalizationKey">The localization key of the expression.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.DataFilterExpression.ToString">
            <summary>
            Creates a string representation of the current object.
            </summary>
            <returns>A new string representing the current object.</returns>
        </member>
        <member name="P:Tekla.Structures.Filtering.DataFilterExpression.Value">
            <summary>
            Gets or sets the value to represent.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.BooleanConstantFilterExpression.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the BooleanConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.BooleanConstantFilterExpression.#ctor(System.Boolean,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the BooleanConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.BooleanFilterExpression">
            <summary>
            The BooleanFilterExpression class represents a Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.BooleanFilterExpression.#ctor(System.String)">
            <summary>
            Initializes a new instance of the BooleanFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.BooleanFilterExpression.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the BooleanFilterExpression class.
            </summary>
            <param name="Name">The name of the expression.</param>
            <param name="Category">The category of the expression.</param>
            <param name="Property">The property of the expression.</param>
            <param name="LocalizationKey">The localization key of the expression.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.BooleanOperatorType">
            <summary>
            The Boolean operator type defines the operators between two Boolean filter expressions.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.BooleanOperatorType.IS_EQUAL">
            <summary>
            The "is equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.BooleanOperatorType.IS_NOT_EQUAL">
            <summary>
            The "is not equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.BooleanOperatorType.BOOLEAN_OR">
            <summary>
            The Boolean OR operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.BooleanOperatorType.BOOLEAN_AND">
            <summary>
            The Boolean AND operator.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions">
            <summary>
            The AssemblyFilterExpressions class contains all the assembly filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.DateTimeFilterExpression">
            <summary>
            The DateTimeFilterExpression class represents a DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.DateTimeFilterExpression.#ctor(System.String)">
            <summary>
            Initializes a new instance of the DateTimeFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.DateTimeFilterExpression.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the DateTimeFilterExpression class.
            </summary>
            <param name="Name">The name of the expression.</param>
            <param name="Category">The category of the expression.</param>
            <param name="Property">The property of the expression.</param>
            <param name="LocalizationKey">The localization key of the expression.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.NumericFilterExpression">
            <summary>
            The NumericFilterExpression class represents a numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericFilterExpression.#ctor(System.String)">
            <summary>
            Initializes a new instance of the NumericFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericFilterExpression.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the NumericFilterExpression class.
            </summary>
            <param name="Name">The name of the expression.</param>
            <param name="Category">The category of the expression.</param>
            <param name="Property">The property of the expression.</param>
            <param name="LocalizationKey">The localization key of the expression.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.StringFilterExpression">
            <summary>
            The StringFilterExpression class represents a string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.StringFilterExpression.#ctor(System.String)">
            <summary>
            Initializes a new instance of the StringFilterExpression class.
            </summary>
            <param name="Key">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.StringFilterExpression.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the StringFilterExpression class.
            </summary>
            <param name="Name">The name of the expression.</param>
            <param name="Category">The category of the expression.</param>
            <param name="Property">The property of the expression.</param>
            <param name="LocalizationKey">The localization key of the expression.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.IdNumber">
            <summary>
            The IdNumber class represents the identifier number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.IdNumber.#ctor">
            <summary>
            Initializes a new instance of the IdNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Level">
            <summary>
            The Level class represents the level filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Level.#ctor">
            <summary>
            Initializes a new instance of the Level class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Name">
            <summary>
            The Name class represents the name filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Name.#ctor">
            <summary>
            Initializes a new instance of the Name class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.PositionNumber">
            <summary>
            The PositionNumber class represents the position number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.PositionNumber.#ctor">
            <summary>
            Initializes a new instance of the PositionNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Prefix">
            <summary>
            The Prefix class represents the prefix filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Prefix.#ctor">
            <summary>
            Initializes a new instance of the Prefix class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Series">
            <summary>
            The Series class represents the series filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Series.#ctor">
            <summary>
            Initializes a new instance of the Series class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.StartNumber">
            <summary>
            The StartNumber class represents the start number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.StartNumber.#ctor">
            <summary>
            Initializes a new instance of the StartNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Type">
            <summary>
            The Type class represents the type filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.Type.#ctor">
            <summary>
            Initializes a new instance of the Type class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions">
            <summary>
            The BoltFilterExpressions class contains all the bolt filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Length">
            <summary>
            The Length class represents the length filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Length.#ctor">
            <summary>
            Initializes a new instance of the Length class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.SiteWorkshop">
            <summary>
            The SiteWorkshop class represents the site/workshop filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.SiteWorkshop.#ctor">
            <summary>
            Initializes a new instance of the SiteWorkshop class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Size">
            <summary>
            The Size class represents the size filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Size.#ctor">
            <summary>
            Initializes a new instance of the Size class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Standard">
            <summary>
            The Standard class represents the standard filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.BoltFilterExpressions.Standard.#ctor">
            <summary>
            Initializes a new instance of the Standard class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions">
            <summary>
            The ComponentFilterExpressions class contains all the component filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.ConnectionCode">
            <summary>
            The ConnectionCode class represents the connection code filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.ConnectionCode.#ctor">
            <summary>
            Initializes a new instance of the ConnectionCode class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.Name">
            <summary>
            The Name class represents the name filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.Name.#ctor">
            <summary>
            Initializes a new instance of the Name class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.RunningNumber">
            <summary>
            The RunningNumber class represents the running number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ComponentFilterExpressions.RunningNumber.#ctor">
            <summary>
            Initializes a new instance of the RunningNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions">
            <summary>
            The LoadFilterExpressions class contains all the load filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.Group">
            <summary>
            The Group class represents the group filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.Group.#ctor">
            <summary>
            Initializes a new instance of the Group class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.Type">
            <summary>
            The Type class represents the type filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LoadFilterExpressions.Type.#ctor">
            <summary>
            Initializes a new instance of the Type class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions">
            <summary>
            The LogicalAreaFilterExpressions class contains all the logical area filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Building">
            <summary>
            The Building class represents the building filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Building.#ctor">
            <summary>
            Initializes a new instance of the Building class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Section">
            <summary>
            The Section class represents the section filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Section.#ctor">
            <summary>
            Initializes a new instance of the Section class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Site">
            <summary>
            The Site class represents the site filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Site.#ctor">
            <summary>
            Initializes a new instance of the Site class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Story">
            <summary>
            The Story class represents the story filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.LogicalAreaFilterExpressions.Story.#ctor">
            <summary>
            Initializes a new instance of the Story class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions">
            <summary>
            The ObjectFilterExpressions class contains all the object filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.Guid">
            <summary>
            The Guid class represents the GUID filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.Guid.#ctor">
            <summary>
            Initializes a new instance of the Guid class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.IdNumber">
            <summary>
            The IdNumber class represents the identifier number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.IdNumber.#ctor">
            <summary>
            Initializes a new instance of the IdNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.IsComponent">
            <summary>
            The IsComponent class represents the "is component" filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.IsComponent.#ctor">
            <summary>
            Initializes a new instance of the IsComponent class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.Type">
            <summary>
            The Type class represents the type filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectFilterExpressions.Type.#ctor">
            <summary>
            Initializes a new instance of the Type class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions">
            <summary>
            The ObjectTypesFilterExpressions class contains all the object type filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CategoryName">
            <summary>
            The CategoryName class represents the category name filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.CategoryName.#ctor">
            <summary>
            Initializes a new instance of the CategoryName class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.EntityName">
            <summary>
            The EntityName class represents the entity name filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ObjectTypesFilterExpressions.EntityName.#ctor">
            <summary>
            Initializes a new instance of the EntityName class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions">
            <summary>
            The PartFilterExpressions class contains all the part filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Class">
            <summary>
            The Class class represents the class filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Class.#ctor">
            <summary>
            Initializes a new instance of the Class class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Finish">
            <summary>
            The Finish class represents the finish filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Finish.#ctor">
            <summary>
            Initializes a new instance of the Finish class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Lot">
            <summary>
            The Lot class represents the lot filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Lot.#ctor">
            <summary>
            Initializes a new instance of the Lot class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Material">
            <summary>
            The Material class represents the material filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Material.#ctor">
            <summary>
            Initializes a new instance of the Material class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Name">
            <summary>
            The Name class represents the name filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Name.#ctor">
            <summary>
            Initializes a new instance of the Name class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.NumberingSeries">
            <summary>
            The NumberingSeries class represents the numbering series filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.NumberingSeries.#ctor">
            <summary>
            Initializes a new instance of the NumberingSeries class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.PositionNumber">
            <summary>
            The PositionNumber class represents the position number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.PositionNumber.#ctor">
            <summary>
            Initializes a new instance of the PositionNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Prefix">
            <summary>
            The Prefix class represents the prefix filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Prefix.#ctor">
            <summary>
            Initializes a new instance of the Prefix class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.PrimaryPart">
            <summary>
            The PrimaryPart class represents the primary part filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.PrimaryPart.#ctor">
            <summary>
            Initializes a new instance of the PrimaryPart class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Profile">
            <summary>
            The Profile class represents the profile filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.Profile.#ctor">
            <summary>
            Initializes a new instance of the Profile class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.PartFilterExpressions.StartNumber">
            <summary>
            The StartNumber class represents the start number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.PartFilterExpressions.StartNumber.#ctor">
            <summary>
            Initializes a new instance of the StartNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions">
            <summary>
            The ReferenceObjectFilterExpressions class contains all the reference object filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReferenceObjectFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions">
            <summary>
            The ReinforcingBarFilterExpressions class contains all the reinforcing bar filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Class">
            <summary>
            The Class class represents the class filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Class.#ctor">
            <summary>
            Initializes a new instance of the Class class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Diameter">
            <summary>
            The Diameter class represents the diameter filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Diameter.#ctor">
            <summary>
            Initializes a new instance of the Diameter class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.JoinType">
            <summary>
            The JoinType class represents the join type filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.JoinType.#ctor">
            <summary>
            Initializes a new instance of the JoinType class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Length">
            <summary>
            The Length class represents the length filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Length.#ctor">
            <summary>
            Initializes a new instance of the Length class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Material">
            <summary>
            The Material class represents the material filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Material.#ctor">
            <summary>
            Initializes a new instance of the Material class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Name">
            <summary>
            The Name class represents the name filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Name.#ctor">
            <summary>
            Initializes a new instance of the Name class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.NumberingSeries">
            <summary>
            The NumberingSeries class represents the numbering series filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.NumberingSeries.#ctor">
            <summary>
            Initializes a new instance of the NumberingSeries class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Position">
            <summary>
            The Position class represents the position filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Position.#ctor">
            <summary>
            Initializes a new instance of the Position class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.PositionNumber">
            <summary>
            The PositionNumber class represents the position number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.PositionNumber.#ctor">
            <summary>
            Initializes a new instance of the PositionNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Prefix">
            <summary>
            The Prefix class represents the prefix filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Prefix.#ctor">
            <summary>
            Initializes a new instance of the Prefix class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Shape">
            <summary>
            The Shape class represents the shape filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Shape.#ctor">
            <summary>
            Initializes a new instance of the Shape class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Size">
            <summary>
            The Size class represents the size filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.Size.#ctor">
            <summary>
            Initializes a new instance of the Size class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.StartNumber">
            <summary>
            The StartNumber class represents the start number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.ReinforcingBarFilterExpressions.StartNumber.#ctor">
            <summary>
            Initializes a new instance of the StartNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions">
            <summary>
            The TaskFilterExpressions class contains all the task filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.ActualEndDate">
            <summary>
            The ActualEndDate class represents the actual end date filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.ActualEndDate.#ctor">
            <summary>
            Initializes a new instance of the ActualEndDate class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.ActualStartDate">
            <summary>
            The ActualStartDate class represents the actual start date filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.ActualStartDate.#ctor">
            <summary>
            Initializes a new instance of the ActualStartDate class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Completeness">
            <summary>
            The Completeness class represents the completeness filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Completeness.#ctor">
            <summary>
            Initializes a new instance of the Completeness class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Critical">
            <summary>
            The Critical class represents the critical filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Critical.#ctor">
            <summary>
            Initializes a new instance of the Critical class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Local">
            <summary>
            The Local class represents the local filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Local.#ctor">
            <summary>
            Initializes a new instance of the Local class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Name">
            <summary>
            The Name class represents the name filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.Name.#ctor">
            <summary>
            Initializes a new instance of the Name class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.PlannedEndDate">
            <summary>
            The PlannedEndDate class represents the planned end date filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.PlannedEndDate.#ctor">
            <summary>
            Initializes a new instance of the PlannedEndDate class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.PlannedStartDate">
            <summary>
            The PlannedStartDate class represents the planned start date filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TaskFilterExpressions.PlannedStartDate.#ctor">
            <summary>
            Initializes a new instance of the PlannedStartDate class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions">
            <summary>
            The TemplateFilterExpressions class contains all the template filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.TemplateFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions">
            <summary>
            The WeldFilterExpressions class contains all the weld filter expressions.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomBoolean">
            <summary>
            The CustomBoolean class represents a custom Boolean filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomBoolean.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomBoolean class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomDateTime">
            <summary>
            The CustomDateTime class represents a custom DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomDateTime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomDateTime class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomNumber">
            <summary>
            The CustomNumber class represents a custom numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomNumber.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomNumber class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomString">
            <summary>
            The CustomString class represents a custom string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.CustomString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CustomString class.
            </summary>
            <param name="UserAttribute">The user attribute to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.Phase">
            <summary>
            The Phase class represents the phase filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.Phase.#ctor">
            <summary>
            Initializes a new instance of the Phase class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.PositionNumber">
            <summary>
            The PositionNumber class represents the position number filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.PositionNumber.#ctor">
            <summary>
            Initializes a new instance of the PositionNumber class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.ReferenceText">
            <summary>
            The ReferenceText class represents the reference text filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.ReferenceText.#ctor">
            <summary>
            Initializes a new instance of the ReferenceText class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.SizeAboveLine">
            <summary>
            The SizeAboveLine class represents the size above line filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.SizeAboveLine.#ctor">
            <summary>
            Initializes a new instance of the SizeAboveLine class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.SizeBelowLine">
            <summary>
            The SizeBelowLine class represents the size below line filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.SizeBelowLine.#ctor">
            <summary>
            Initializes a new instance of the SizeBelowLine class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.TypeAboveLine">
            <summary>
            The TypeAboveLine class represents the type above line filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.TypeAboveLine.#ctor">
            <summary>
            Initializes a new instance of the TypeAboveLine class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.TypeBelowLine">
            <summary>
            The TypeBelowLine class represents the type below line filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.TypeBelowLine.#ctor">
            <summary>
            Initializes a new instance of the TypeBelowLine class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.WeldingSite">
            <summary>
            The WeldingSite class represents the welding site filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.Categories.WeldFilterExpressions.WeldingSite.#ctor">
            <summary>
            Initializes a new instance of the WeldingSite class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.DateTimeConstantFilterExpression">
            <summary>
            The DateTimeConstantFilterExpression class represents a constant DateTime filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.DateTimeConstantFilterExpression.#ctor(System.DateTime)">
            <summary>
            Initializes a new instance of the DateTimeConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.DateTimeConstantFilterExpression.#ctor(System.DateTime,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the DateTimeConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.DateTimeOperatorType">
            <summary>
            The DateTime operator type defines the operators between two DateTime filter expressions.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.DateTimeOperatorType.IS_EQUAL">
            <summary>
            The "is equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.DateTimeOperatorType.IS_NOT_EQUAL">
            <summary>
            The "is not equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.DateTimeOperatorType.EARLIER_THAN">
            <summary>
            The "earlier than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.DateTimeOperatorType.EARLIER_OR_EQUAL">
            <summary>
            The "earlier or equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.DateTimeOperatorType.LATER_THAN">
            <summary>
            The "later than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.DateTimeOperatorType.LATER_OR_EQUEL">
            <summary>
            The "later or equal" operator.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.Filter">
            <summary>
            The Filter class creates a filter file based on the input <see cref="P:Tekla.Structures.Filtering.Filter.FilterExpression"/> object.
            </summary>
            <seealso cref="T:Tekla.Structures.Filtering.BinaryFilterExpression"/>
            <seealso cref="T:Tekla.Structures.Filtering.BinaryFilterExpressionCollection"/>
            <example>
            The following example creates a Tekla Structures object group selection filter file.
            
            The expression generated is the following:
            (PartName == BEAM1 OR PartName == BEAM2 OR PartName == BEAM3 OR PartComment StartsWith test)
            
            <code>
            using System;
            using System.IO;
            using Tekla.Structures.Filtering;
            using Tekla.Structures.Filtering.Categories;
            
            public class FilterExample
            {
                   // Creates a selection filter for the following filter expression:
                   // (PartName == BEAM1 OR PartName == BEAM2 OR PartName == BEAM3 OR PartComment StartsWith test)
                   public void CreateSelectionFilter()
                   {
                       // Creates the filter expressions
                       PartFilterExpressions.Name PartName = new PartFilterExpressions.Name();
                       StringConstantFilterExpression Beam1 = new StringConstantFilterExpression("BEAM1");
                       StringConstantFilterExpression Beam2 = new StringConstantFilterExpression("BEAM2");
                       StringConstantFilterExpression Beam3 = new StringConstantFilterExpression("BEAM3");
            
                       // Creates a custom part filter
                       PartFilterExpressions.CustomString PartComment = new PartFilterExpressions.CustomString("Comment");
                       StringConstantFilterExpression Test = new StringConstantFilterExpression("test");
            
                       // Creates the binary filter expressions
                       BinaryFilterExpression Expression1 = new BinaryFilterExpression(PartName, StringOperatorType.IS_EQUAL, Beam1);
                       BinaryFilterExpression Expression2 = new BinaryFilterExpression(PartName, StringOperatorType.IS_EQUAL, Beam2);
                       BinaryFilterExpression Expression3 = new BinaryFilterExpression(PartName, StringOperatorType.IS_EQUAL, Beam3);
                       BinaryFilterExpression Expression4 = new BinaryFilterExpression(PartComment, StringOperatorType.STARTS_WITH, Test);
            
                       // Creates the binary filter expression collection
                       BinaryFilterExpressionCollection ExpressionCollection = new BinaryFilterExpressionCollection();
                       ExpressionCollection.Add(new BinaryFilterExpressionItem(Expression1, BinaryFilterOperatorType.BOOLEAN_OR));
                       ExpressionCollection.Add(new BinaryFilterExpressionItem(Expression2, BinaryFilterOperatorType.BOOLEAN_OR));
                       ExpressionCollection.Add(new BinaryFilterExpressionItem(Expression3, BinaryFilterOperatorType.BOOLEAN_OR));
                       ExpressionCollection.Add(new BinaryFilterExpressionItem(Expression4));
            
                       string AttributesPath = Path.Combine(@"c:\modelPath", "attributes");
                       string FilterName = Path.Combine(AttributesPath, "filter");
            
                       Filter Filter = new Filter(ExpressionCollection);
                       // Generates the filter file
                       Filter.CreateFile(FilterExpressionFileType.OBJECT_GROUP_SELECTION, FilterName);
                   }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Filtering.Filter.#ctor(Tekla.Structures.Filtering.FilterExpression)">
            <summary>
            Initializes a new instance of the Filter class.
            </summary>
            <param name="FilterExpression">The <see cref="P:Tekla.Structures.Filtering.Filter.FilterExpression"/> object to be generated.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null <paramref name="FilterExpression"/> is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.Filter.#ctor(System.String,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the Filter class.
            </summary>
            <param name="FullFileName">The filter's full file name to load.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to correctly parse numbers
            and dates according to the current culture information. If not provided, the current CultureInfo is used.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null <paramref name="FullFileName"/> is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.Filtering.Filter.CreateFile(Tekla.Structures.Filtering.FilterExpressionFileType,System.String)">
            <summary>
            Creates a filter and saves it as a text file.
            </summary>
            <param name="FilterExpressionFileType">The <see cref="T:Tekla.Structures.Filtering.FilterExpressionFileType"/> to be generated.</param>
            <param name="FullFileName">The full file name of the file to be saved.</param>
            <returns>A string containing the filter's full file name.</returns>
        </member>
        <member name="M:Tekla.Structures.Filtering.Filter.ToString">
            <summary>
             Returns the current <see cref="P:Tekla.Structures.Filtering.Filter.FilterExpression"/> as a string.
            </summary>
            <returns>A string representation of the current <see cref="P:Tekla.Structures.Filtering.Filter.FilterExpression"/>.</returns>
        </member>
        <member name="P:Tekla.Structures.Filtering.Filter.FilterExpression">
            <summary>
            Gets the current <see cref="P:Tekla.Structures.Filtering.Filter.FilterExpression"/> instance.
            </summary>
            <exception cref="T:System.NullReferenceException">Thrown when a null value is specified.</exception>
        </member>
        <member name="T:Tekla.Structures.Filtering.FilterExpressionFileType">
            <summary>
            The filter expression file type defines the filter expression file types.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.FilterExpressionFileType.OBJECT_GROUP_SELECTION">
            <summary>
            The object group selection filter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.FilterExpressionFileType.OBJECT_GROUP_VIEW">
            <summary>
            The object group view filter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.FilterExpressionFileType.DRAWING_SINGLE_PART">
            <summary>
            The drawing single part filter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.FilterExpressionFileType.DRAWING_ASSEMBLY">
            <summary>
            The drawing assembly filter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.FilterExpressionFileType.DRAWING_CAST_UNIT">
            <summary>
            The drawing cast unit filter.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.FilterExpressionFileType.DRAWING_GENERAL">
            <summary>
            The drawing general filter.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.#ctor">
            <summary>
            Initializes a new instance of the FilterExpressionGenerator class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.Generate(Tekla.Structures.Filtering.FilterExpression)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object and returns it as a string.
            </para>
            </summary>
            <param name="FilterExpression">The <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object to be generated.</param>
            <returns>A string containing the filter.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when a null <paramref name="FilterExpression"/> is specified.</exception>
            <exception cref="T:Tekla.Structures.Filtering.InvalidFilterExpressionException">
            Thrown when an invalid <paramref name="FilterExpression"/> is specified.
            </exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.Generate(Tekla.Structures.Filtering.FilterExpression,Tekla.Structures.Filtering.FilterExpressionFileType,System.String)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object and saves it as a text file.
            </para>
            </summary>
            <param name="FilterExpression">The <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object to be generated.</param>
            <param name="FilterExpressionFileType">The <see cref="T:Tekla.Structures.Filtering.FilterExpressionFileType"/> to be generated.</param>
            <param name="FullFileName">The full file name of the file to be saved.</param>
            <returns>A string containing the filter's full file name.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown when a null <paramref name="FilterExpression"/> or <paramref name="FullFileName"/> is specified.
            </exception>
            <exception cref="T:Tekla.Structures.Filtering.InvalidFilterExpressionException">
            Thrown when an invalid <paramref name="FilterExpression"/> is specified.
            </exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.Generate(Tekla.Structures.Filtering.FilterExpression,System.IO.TextWriter)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified FilterExpression object and uses a TextWriter for the output.
            Override this method to change the class behavior.
            </para>
            </summary>
            <param name="FilterExpression">The <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object to be generated.</param>
            <param name="TextWriter">The TextWriter object for the output.</param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GenerateExpression(Tekla.Structures.Filtering.Expression,System.IO.TextWriter)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified Expression object and uses a TextWriter for the output.
            Override this method to change the class behavior.
            </para>
            </summary>
            <param name="Expression">The Expression object to be generated.</param>
            <param name="TextWriter">The TextWriter object for the output.</param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GenerateBinaryFilterExpression(Tekla.Structures.Filtering.BinaryFilterExpression,System.IO.TextWriter)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified BinaryFilterExpression object and uses a TextWriter for the output.
            Override this method to change the class behavior.
            </para>
            </summary>
            <param name="BinaryFilterExpression">The BinaryFilterExpression object to be generated.</param>
            <param name="TextWriter">The TextWriter object for the output.</param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GenerateFilterExpression(Tekla.Structures.Filtering.DataFilterExpression,System.IO.TextWriter)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified DataFilterExpression object and uses a TextWriter for the output.
            Override this method to change the class behavior.
            </para>
            </summary>
            <param name="FilterExpression">The DataFilterExpression object to be generated.</param>
            <param name="TextWriter">The TextWriter object for the output.</param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GenerateOperatorType(Tekla.Structures.Filtering.OperatorType,System.IO.TextWriter)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified OperatorType enum and uses a TextWriter for the output.
            Override this method to change the class behavior.
            </para>
            </summary>
            <param name="OperatorType">The OperatorType object to be generated.</param>
            <param name="TextWriter">The TextWriter object for the output.</param>
            <exception cref="T:System.NotSupportedException">Thrown when a not supported <paramref name="OperatorType"/> is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GenerateBinaryFilterItemOperatorType(Tekla.Structures.Filtering.BinaryFilterOperatorType,System.IO.TextWriter)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified BinaryFilterOperatorType enum and uses a TextWriter for the output.
            Override this method to change the class behavior.
            </para>
            </summary>
            <param name="BinaryFilterItemOperatorType">The BinaryFilterOperatorType object to be generated.</param>
            <param name="TextWriter">The TextWriter object for the output.</param>
            <exception cref="T:System.NotSupportedException">
            Thrown when a not supported <paramref name="BinaryFilterItemOperatorType"/> is specified.
            </exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GenerateBinaryFilterExpressionCollection(Tekla.Structures.Filtering.BinaryFilterExpressionCollection,System.IO.TextWriter)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Generates a filter for the specified BinaryFilterExpressionCollection object and uses a TextWriter for the output.
            Override this method to change the class behavior.
            </para>
            </summary>
            <param name="BinaryFilterExpressionCollection">The BinaryFilterExpressionCollection object to be generated.</param>
            <param name="TextWriter">The TextWriter object for the output.</param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GetFilterExpressionsCount(Tekla.Structures.Filtering.Expression,System.Int32@)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Gets the number of FilterExpressions in the current Expression.
            </para>
            </summary>
            <param name="Expression">The Expression to be analyzed.</param>
            <param name="Count">The number of the filter expressions.</param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGenerator.GetFilterExpressionFileExtension(Tekla.Structures.Filtering.FilterExpressionFileType)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Gets the filter file extension.
            </para>
            </summary>
            <param name="FilterExpressionFileType">The filter file type.</param>
            <returns>A string representing the file extension.</returns>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorFactory">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorFactory.CreateGenerator">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Creates a filter generator.
            </para>
            </summary>
            <returns>A new filter generator.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorFactory.CreateGenerator(Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorType)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Creates a filter generator of a specific type.
            </para>
            </summary>
            <param name="FilterExpressionGeneratoryType">The filter generator type to be used.</param>
            <returns>A new filter generator.</returns>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorType">
            <summary>
            The filter expression generator type defines the filter expression generators.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorType.TEKLA">
            <summary>
            The Tekla Structures filter expression.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorType.XML">
            <summary>
            The XML filter expression (to be used only for test purposes).
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionGeneratorType.C">
            <summary>
            The C language filter expression (to be used only for test purposes).
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.#ctor">
            <summary>
            Initializes a new instance of the FilterExpressionParser class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.Parse(System.String,System.IFormatProvider)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Parse a filter file and returns a <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.
            </para>
            </summary>
            <param name="FullFileName">The filter file full name.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info. If not provided
            it uses the current CultureInfo.</param>
            <returns>A <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when a null or empty <paramref name="FullFileName"/> is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseExpressionString(System.String,System.IFormatProvider)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Parse a filter file and returns a <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.
            </para>
            </summary>
            <param name="FilterString">The filter expression as a text string.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info. If not provided
            it uses the current CultureInfo.</param>
            <returns>A <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when a null or empty <paramref name="FilterString"/> is specified.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.FindDataFilterExpression``1(System.String)">
            <summary>
            Finds a <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived object in the running assembly
            and compare the default value to the input string.
            </summary>
            <typeparam name="T">A <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived type.</typeparam>
            <param name="InputString">The input string to compare.</param>
            <returns>A new instance of the <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived class.</returns>
            <exception cref="T:System.NotSupportedException">Thrown when a the input string is not found.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.CountOccurrence(System.Collections.Generic.IEnumerable{System.Char},System.Char)">
            <summary>
            Counts the occurrence of a specific char in a string.
            </summary>
            <param name="InputString">The input string to check.</param>
            <param name="InputChar">The input char to count.</param>
            <returns>The number of occurrence of a specific char.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseString(System.String,System.IFormatProvider)">
            <summary>
            Parses the input FilterString.
            
            Override this method to change the class behavior.
            </summary>
            <param name="FilterString">The filter expression as a text string.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info. If not provided
            it uses the current CultureInfo.</param>
            <returns>A <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseBinaryFilterExpression(System.String,System.IFormatProvider)">
            <summary>
            Parses the input BinaryFilterExpressionString using the <see cref="T:System.IFormatProvider"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="BinaryFilterExpressionString">The binary filter expression string.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info. </param>
            <returns>A new <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input binary filter expression string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.GetBinaryFilterExpressionElements(System.String)">
            <summary>
            Gets the binary filter expression as array of tree elements. 
            The first represents the left operand, the second the Binary Operator, the third the Value.
            
            Override this method to change the class behavior.
            </summary>
            <param name="BinaryFilterExpressionString">The input binary filter expression string.</param>
            <returns>Returns an array of tree elements containing the two binary filter expression operand and one operator.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseLeftOperand(System.String)">
            <summary>
            Parses the left operand of the binary filter expression returning a new instance of the <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived object.
            
            Override this method to change the class behavior.
            </summary>
            <param name="BinaryFilterExpressionString">The binary filter expression string.</param>
            <returns>A new instance of the <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived class representing the Left operand.</returns>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws an <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if the filter string contains errors.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseStringConstantFilterExpression(System.String)">
            <summary>
            Parses a string returning a new <see cref="T:Tekla.Structures.Filtering.StringConstantFilterExpression"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="StringConstantString">The input string to parse.</param>
            <returns>A new <see cref="T:Tekla.Structures.Filtering.StringConstantFilterExpression"/>.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseStringOperatorType(System.String)">
            <summary>
            Parse a string returnin a <see cref="T:Tekla.Structures.Filtering.StringOperatorType"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="StringOperatorTypeString">The input string to parse.</param>
            <returns>A <see cref="T:Tekla.Structures.Filtering.StringOperatorType"/> enum.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseNumericConstantFilterExpression(System.String,System.IFormatProvider)">
            <summary>
            Parses a string returning a new <see cref="T:Tekla.Structures.Filtering.NumericConstantFilterExpression"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="NumericConstantString">The input string to parse.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info. </param>
            <returns>A new <see cref="T:Tekla.Structures.Filtering.NumericConstantFilterExpression"/>.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseNumericOperatorType(System.String)">
            <summary>
            Parses a string returning a <see cref="T:Tekla.Structures.Filtering.NumericOperatorType"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="NumericOperatorString">The input string to parse.</param>
            <returns>A <see cref="T:Tekla.Structures.Filtering.NumericOperatorType"/> enum.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseDateTimeConstantFilterExpression(System.String,System.IFormatProvider)">
            <summary>
            Parses a string returning a new <see cref="T:Tekla.Structures.Filtering.DateTimeConstantFilterExpression"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="DateTimeConstantString"></param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info. </param>
            <returns>A new <see cref="T:Tekla.Structures.Filtering.DateTimeConstantFilterExpression"/>.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseDateTimeOperatorType(System.String)">
            <summary>
            Parses a string returning a <see cref="T:Tekla.Structures.Filtering.DateTimeOperatorType"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="DateTimeOperatorString">The input string to parse.</param>
            <returns>A <see cref="T:Tekla.Structures.Filtering.DateTimeOperatorType"/> enum.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseBooleanConstantFilterExpression(System.String)">
            <summary>
            Parses a string returning a new <see cref="T:Tekla.Structures.Filtering.BooleanConstantFilterExpression"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="BooleanConstantString">The input string to parse.</param>
            <returns>A new <see cref="T:Tekla.Structures.Filtering.DateTimeConstantFilterExpression"/>.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.ParseBooleanOperatorType(System.String)">
            <summary>
            Parses a string returning a <see cref="T:Tekla.Structures.Filtering.BooleanOperatorType"/>.
            
            Override this method to change the class behavior.
            </summary>
            <param name="BooleanOperatorTypeString">The input string to parse.</param>
            <returns>A <see cref="T:Tekla.Structures.Filtering.BooleanOperatorType"/> enum.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParser.Tokenizer(System.String)">
            <summary>
            Splits the current filter strings in multiple <see cref="T:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken"/>.
            Each token contains a single filter section or a collection of sub filters sections.
            </summary>
            <param name="FilterString">The filter string to parse.</param>
            <returns>A list of <see cref="T:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken"/> objects.</returns>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws an <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if the filter string contains errors.</exception>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParserFactory">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParserFactory.CreateParser">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Creates a filter parser.
            </para>
            </summary>
            <returns>A new filter parser.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParserFactory.CreateParser(Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParserType)">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            <para>
            Creates a filter parser of a specific type.
            </para>
            </summary>
            <param name="FilterExpressionParserType">The filter parser type to be used.</param>
            <returns>A new filter parser.</returns>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParserType">
            <summary>
            The filter expression parser type defines the filter expression parser.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParserType.TEKLA">
            <summary>
            The Tekla Structures filter expression.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.FilterExpressionParserType.C">
            <summary>
            The C language filter expression (to be used only for test purposes).
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken">
            <summary>
            This API supports the Open API infrastructure and is not intended to be used directly from your code. Use the <see cref="T:Tekla.Structures.Filtering.Filter"/> class instead.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken.#ctor(System.String,Tekla.Structures.Filtering.BinaryFilterOperatorType,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the BinaryFilterExpressionToken class.
            </summary>
            <param name="FilterString">The FilterString property.</param>
            <param name="BinaryFilterOperatorType">The BinaryFilterOperator property.</param>
            <param name="IsCollection">True is a collection, otherwise false.</param>
            <param name="Enabled">True is enabled otherwise false.</param>
        </member>
        <member name="P:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken.FilterString">
            <summary>
            Gets or sets the FilterString string.
            </summary>
        </member>
        <member name="P:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken.BinaryFilterOperatorType">
            <summary>
            Gets or sets the BinaryFilterOperatorType.
            </summary>
        </member>
        <member name="P:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken.IsCollection">
            <summary>
            Gets or sets the IsCollection property.
            </summary>
        </member>
        <member name="P:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken.Enabled">
            <summary>
            Gets or sets the Enabled property.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.Generators.TeklaFilterExpressionGenerator">
            <summary>
            Represents the Tekla filter expression generator. Is used to generate the tekla filter text stream.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.Generators.XmlFilterExpressionGenerator">
            <summary>
            Generates an Xml text stream representing a FilterExpression. This class cannot be inherited.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.InvalidFilterExpressionException">
            <summary>
            The InvalidFilterExpressionException class represents an error that occurred during the expression evaluation.
            This class cannot be inherited.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.InvalidFilterExpressionException.#ctor(Tekla.Structures.Filtering.Expression,Tekla.Structures.Filtering.InvalidFilterExpressionExceptionReasonsType)">
            <summary>
            Initializes a new instance of the InvalidFilterExpressionException class.
            </summary>
            <param name="Expression">The invalid expression.</param>
            <param name="InvalidFilterExpressionExceptionReasonsType">The reason why the exception is thrown.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.InvalidFilterExpressionException.#ctor(Tekla.Structures.Filtering.Expression,Tekla.Structures.Filtering.OperatorType,Tekla.Structures.Filtering.Expression,Tekla.Structures.Filtering.InvalidFilterExpressionExceptionReasonsType)">
            <summary>
            Initializes a new instance of the InvalidFilterExpressionException class.
            </summary>
            <param name="LeftExpression">The invalid expression's left operand.</param>
            <param name="OperatorType">The invalid expression's operator type.</param>
            <param name="RightExpression">The invalid expression's right operand.</param>
            <param name="InvalidFilterExpressionExceptionReasonsType">The reason why the exception is thrown.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.InvalidFilterExpressionException.#ctor(Tekla.Structures.Filtering.Expression,Tekla.Structures.Filtering.InvalidFilterExpressionExceptionReasonsType,System.Int32)">
            <summary>
            Initializes a new instance of the InvalidFilterExpressionException class.
            </summary>
            <param name="Expression">The invalid expression.</param>
            <param name="InvalidFilterExpressionExceptionReasonsType">The reason why the exception is thrown.</param>
            <param name="MaximumExpressionNumber">The maximun number of expressions.</param>
        </member>
        <member name="P:Tekla.Structures.Filtering.InvalidFilterExpressionException.Expression">
            <summary>
            Gets the invalid expression.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.InvalidFilterExpressionException.InvalidFilterExpressionExceptionReasonsType">
            <summary>
            Gets the reason why the exception is thrown.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.InvalidFilterExpressionException.LeftExpression">
            <summary>
            Gets the invalid expression's left operand.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.InvalidFilterExpressionException.RightExpression">
            <summary>
            Gets the invalid expression's right operand.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Filtering.InvalidFilterExpressionException.OperatorType">
            <summary>
            Gets the invalid expression's operator.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.InvalidFilterExpressionExceptionReasonsType">
            <summary>
            The invalid filter expression exception reasons type defines the possible reasons for the
            InvalidFilterExpressionException.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.InvalidFilterExpressionExceptionReasonsType.TOO_MANY_NESTED_COLLECTIONS">
            <summary>
            There are too many nested collections.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.NumericConstantFilterExpression">
            <summary>
            The NumericConstantFilterExpression class represents a constant numeric filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(Tekla.Structures.TeklaStructuresDatabaseTypeEnum)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="ObjectType">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Int16)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.UInt16)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.UInt32)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Int64)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.UInt64)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Int16,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.UInt16,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Int32,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.UInt32,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Int64,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.UInt64,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="M:Tekla.Structures.Filtering.NumericConstantFilterExpression.#ctor(System.Double,System.IFormatProvider)">
            <summary>
            Initializes a new instance of the NumericConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
            <param name="Provider">An IFormatProvider object.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.NumericOperatorType">
            <summary>
            The numeric operator type defines the operators between two numeric filter expressions.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.NumericOperatorType.IS_EQUAL">
            <summary>
            The "is equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.NumericOperatorType.IS_NOT_EQUAL">
            <summary>
            The "is not equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.NumericOperatorType.SMALLER_THAN">
            <summary>
            The "smaller than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.NumericOperatorType.SMALLER_OR_EQUAL">
            <summary>
            The "smaller or equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.NumericOperatorType.GREATER_THAN">
            <summary>
            The "greater than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.NumericOperatorType.GREATER_OR_EQUAL">
            <summary>
            The "greater or equal" operator.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Filtering.OperatorType">
            <summary>
            The operator type defines the operators between two filter expressions.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.IS_EQUAL">
            <summary>
            The "is equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.IS_NOT_EQUAL">
            <summary>
            The "is not equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.BOOLEAN_OR">
            <summary>
            The Boolean OR operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.BOOLEAN_AND">
            <summary>
            The Boolean AND operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.SMALLER_THAN">
            <summary>
            The "smaller than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.SMALLER_OR_EQUAL">
            <summary>
            The "smaller or equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.GREATER_THAN">
            <summary>
            The "greater than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.GREATER_OR_EQUAL">
            <summary>
            The "greater or equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.CONTAINS">
            <summary>
            The "contains" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.NOT_CONTAINS">
            <summary>
            The "not contains" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.STARTS_WITH">
            <summary>
            The "starts with" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.NOT_STARTS_WITH">
            <summary>
            The "not starts with" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.ENDS_WITH">
            <summary>
            The "ends with" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.NOT_ENDS_WITH">
            <summary>
            The "not ends with" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.EARLIER_THAN">
            <summary>
            The "earlier than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.EARLIER_OR_EQUAL">
            <summary>
            The "earlier or equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.LATER_THAN">
            <summary>
            The "later than" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.OperatorType.LATER_OR_EQUAL">
            <summary>
            The "later or equal" operator.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser">
            <summary>
            Represents the Tekla filter expression parser. Is used to parse the tekla filter files.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.TokenElementsCount">
            <summary>
            Represents the number of elements inside a object group.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.SectionHeader">
            <summary>
            Represents the Section Header in the filter file.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.FileHeader">
            <summary>
            Represents the filter file header.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.LeftParenthesisIndex">
            <summary>
            Represent the Left Parenthesis index in the section token.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.EnabledIndex">
            <summary>
            Represent the Enabled index in the section token.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.CategoryIndex">
            <summary>
            Represent the Category index in the section token.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.PropertyIndex">
            <summary>
            Represent the Property index in the section token.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.BinaryOperatorIndex">
            <summary>
            Represent the Binary Operator index in the section token.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.ValueIndex">
            <summary>
            Represent the Value index in the section token.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.RightParenthesisIndex">
            <summary>
            Represent the Right Parenthesis index in the section token.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.OperatorIndex">
            <summary>
            Represent the Operator index in the section token.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.NormalizeFilterExpression(System.String)">
            <summary>
            Normalizes the filter string in order to make it compatible with the Parser.
            This is required in special scenario, for example when the filter expression
            contains collection but doesn't have parenthesis.
            </summary>
            <param name="FilterString">The filter file in string format.</param>
            <returns>Returns the filter file normalized.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.RemoveFilterHeaderAndFooter(System.String)">
            <summary>
            Removes the header and footer from the filter file. It also changes the carriage return to a space.
            </summary>
            <param name="FilterString">The filter file in string format.</param>
            <returns>Returns the filter file body.</returns>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws a <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if no header is found.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.GetFirstParentesisIndexAndCount(System.String,System.Int32@)">
            <summary>
            Gets the index of the char representing the first open parenthesis and the number of parenthesis.
            </summary>
            <param name="FilterString">The filter section string.</param>
            <param name="ParenthesisCount">The number of parenthesis.</param>
            <returns>The index of the char representing the first open parenthesis. If Parenthesis count == 0 returns -1.</returns>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws a <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if no section header is found.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.GetLastParentesisIndexAndCount(System.String,System.Int32@)">
            <summary>
            Gets the index of the char representing the last open parenthesis and the number of parenthesis.
            </summary>
            <param name="FilterString">The filter section string.</param>
            <param name="ParenthesisCount">The number of parenthesis.</param>
            <returns>The index of the char representing the last open parenthesis.</returns>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws a <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if no section header is found.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.GetSectionTokenContent(System.String,System.Int32@,System.Int32@,System.Int32@,System.String@,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            Gets the section token contents.
            </summary>
            <param name="FilterString">The filter section string.</param>
            <param name="LeftParenthesis">The left parenthesis count.</param>
            <param name="RightParenthesis">The right parenthesis count.</param>
            <param name="Enabled">The enable field.</param>
            <param name="Category">The category field.</param>
            <param name="Property">The property field.</param>
            <param name="BinaryOperator">The binary operator field.</param>
            <param name="Value">The value field.</param>
            <param name="Operator">The operator field.</param>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws an <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if the section contains invalid data.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.FindDataFilterExpression(System.String,System.String)">
            <summary>
            Searches in the ExecutingAssembly classes derived from <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/>. If found
            checks if the Category and Property properties are equal to the one passed as parameter.
            </summary>
            <param name="Category">The category to search.</param>
            <param name="Property">The property to search.</param>
            <returns>A new instance of the <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived class.</returns>
            <exception cref="T:System.NotSupportedException">Throws <see cref="T:System.NotSupportedException"/> if no maching classes are found.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.UpdateParenthesis(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Updates the parenthesis count of a section.
            </summary>
            <param name="FilterString">The filter section string.</param>
            <param name="FirstParenthesisIndex">The first parenthesis index.</param>
            <param name="FirstParenthesisCount">The first parenthesis count.</param>
            <param name="LastParenthesisIndex">The last parenthesis index.</param>
            <param name="LastParentesisCount">The last parenthesis count.</param>
            <returns>Returns a filter section string with updated first and last parenthesis.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.ParseString(System.String,System.IFormatProvider)">
            <summary>
            Parse a filter string and returns a new <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.
            </summary>
            <param name="FilterString">The filter string to parse.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info.</param>
            <returns>A new <see cref="T:Tekla.Structures.Filtering.FilterExpression"/> object.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.Tokenizer(System.String)">
            <summary>
            Splits the current filter strings in multiple <see cref="T:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken"/>.
            Each token contains a single filter section or a collection of sub filters sections.
            </summary>
            <param name="FilterString">The filter string to parse.</param>
            <returns>A list of <see cref="T:Tekla.Structures.TeklaStructuresInternal.Filtering.BinaryFilterExpressionToken"/> objects.</returns>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws an <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if the filter string contains errors.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.GetBinaryFilterExpressionElements(System.String)">
            <summary>
            Gets the binary filter expression as array of tree elements. 
            The first represents the left operand in the format "Category+Property", the second the Binary Operator, the third the Value.
            </summary>
            <param name="BinaryFilterExpressionString">The input binary filter expression string.</param>
            <returns>Returns an array of tree elements containing the two binary filter expression operand and one operator.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.ParseLeftOperand(System.String)">
            <summary>
            Parses the left operand of the binary filter expression returning a new instance of the <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived object.
            </summary>
            <param name="BinaryFilterExpressionString">The binary filter expression string.</param>
            <returns>A new instance of the <see cref="T:Tekla.Structures.Filtering.DataFilterExpression"/> derived class representing the Left operand.</returns>
            <exception cref="T:Tekla.Structures.Filtering.FilterExpressionParserException">Throws an <see cref="T:Tekla.Structures.Filtering.FilterExpressionParserException"/> if the filter string contains errors.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.ParseStringOperatorType(System.String)">
            <summary>
            Parses a string operator type string and returns the correct StringOperatorType enum.
            </summary>
            <param name="StringOperatorTypeString">The input string operator type string.</param>
            <returns>If found returns the correct StringOperatorType enum.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> if the string operator type is not found.</exception>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Filtering.Parsers.TeklaFilterExpressionParser.ParseNumericConstantFilterExpression(System.String,System.IFormatProvider)">
            <summary>
            Parses a string returning a new <see cref="T:Tekla.Structures.Filtering.NumericConstantFilterExpression"/>.
            
            This override handles the special case of ObjectType.
            </summary>
            <param name="NumericConstantString">The input string to parse.</param>
            <param name="Provider">An object that supports the <see cref="T:System.IFormatProvider"/> interface to parse correctly numbers and dates according to the current culture info. </param>
            <returns>A new <see cref="T:Tekla.Structures.Filtering.NumericConstantFilterExpression"/>.</returns>
            <exception cref="T:System.NotSupportedException">Throws an <see cref="T:System.NotSupportedException"/> exception if the input string is not supported.</exception>
        </member>
        <member name="T:Tekla.Structures.Filtering.StringConstantFilterExpression">
            <summary>
            The StringConstantFilterExpression class represents a constant string filter expression.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Filtering.StringConstantFilterExpression.#ctor(System.String)">
            <summary>
            Initializes a new instance of the StringConstantFilterExpression class.
            </summary>
            <param name="Value">The value to represent.</param>
        </member>
        <member name="T:Tekla.Structures.Filtering.StringOperatorType">
            <summary>
            The string operator type defines the operators between two string filter expressions.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.IS_EQUAL">
            <summary>
            The "is equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.IS_NOT_EQUAL">
            <summary>
            The "is not equal" operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.CONTAINS">
            <summary>
            The "contains" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.NOT_CONTAINS">
            <summary>
            The "not contains" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.STARTS_WITH">
            <summary>
            The "starts with" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.NOT_STARTS_WITH">
            <summary>
            The "not starts with" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.ENDS_WITH">
            <summary>
            The "ends with" string operator.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Filtering.StringOperatorType.NOT_ENDS_WITH">
            <summary>
            The "not ends with" string operator.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.PolyLine">
            <summary>
            The PolyLine class represents a line that consists of one or more line segments.
            To create a polyline, you have to give a list of the points that will form the polyline.
            The first point in the list will be connected with the second point in the list,
            the second point in the list will be connected with the third point in the list, etc.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.PolyLine.#ctor(System.Collections.IEnumerable)">
            <summary>
            Instantiates a polyline with the given Points.
            </summary>
            <param name="Points">A list of the points that will form the polyline.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.PolyLine.Equals(System.Object)">
            <summary>
            Returns true if the objects are equal.
            </summary>
            <param name="O">The object that equality is wished to be checked with.</param>
            <returns>True if the objects are equal.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.PolyLine.Length">
            <summary>
            Returns the length of a polyline.
            </summary>
            <returns>The length of the polyline.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.PolyLine.GetHashCode">
            <summary>
            Returns a hash code for a polyline.
            Notice, in extremely rare cases, you might not get the same
            hash code for two polylines even though they are considered equal! This
            should, however, happen only in extremely rare cases!
            </summary>
            <returns>The hash code for the polyline.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.PolyLine.op_Equality(Tekla.Structures.Geometry3d.PolyLine,Tekla.Structures.Geometry3d.PolyLine)">
            <summary>
            Checks the equality of two polylines.
            </summary>
            <param name="PolyLine1">The first polyline to be used.</param>
            <param name="PolyLine2">The second polyline to be used.</param>
            <returns>True if the two polylines are equal.
            False otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.PolyLine.op_Inequality(Tekla.Structures.Geometry3d.PolyLine,Tekla.Structures.Geometry3d.PolyLine)">
            <summary>
            Checks the inequality of two polylines.
            </summary>
            <param name="PolyLine1">The first polyline to be used.</param>
            <param name="PolyLine2">The second polyline to be used.</param>
            <returns>True if the two polylines are not equal.
            False otherwise.</returns>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.PolyLine.Points">
            <summary>
            The points the polyline consists of.
            </summary>
        </member>
        <member name="T:Tekla.Structures.ModuleManager">
            <summary>
            The ModuleManager class handles the product model module configuration information: the
            information that defines what configuration the customer is currently running.
            </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._Configuration">
            <summary> Configuration info </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._MultimaterialModeling">
            <summary> "MM" </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._LoadModeling">
            <summary> "LM" </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._SteelDetailing">
            <summary> "SD" </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._ConcreteDetailing">
            <summary> "CD" </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._RebarModeling">
            <summary> "RM" </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._TaskManagement">
            <summary> "TM" </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager._AnalysisAndDesign">
            <summary> "AD" </summary>
        </member>
        <member name="M:Tekla.Structures.ModuleManager.#cctor">
            <summary> Initializes currently available modules. </summary>
        </member>
        <member name="M:Tekla.Structures.ModuleManager.GetModules">
            <summary> Must be called once. </summary>
            <returns>true</returns>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.Configuration">
            <summary> The currently running configuration of Tekla Structures. </summary>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.MultimaterialModeling">
            <summary> Indicates whether the multimaterial modeling is enabled. </summary>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.LoadModeling">
            <summary> Indicates whether the load modeling is enabled. </summary>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.SteelDetailing">
            <summary> Indicates whether the steel detailing is enabled. </summary>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.ConcreteDetailing">
            <summary> Indicates whether the concrete detailing is enabled. </summary>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.RebarModeling">
            <summary> Indicates whether the rebar modeling is enabled. </summary>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.AnalysisAndDesign">
            <summary> Indicates whether the Analysis and Design management is enabled. </summary>
        </member>
        <member name="P:Tekla.Structures.ModuleManager.TaskManagement">
            <summary> Indicates whether the task management is enabled. </summary>
        </member>
        <member name="T:Tekla.Structures.ModuleManager.ProgramConfigurationEnum">
            <summary> The configuration information of the program. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_VIEWER">
            <summary> The viewer configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_DRAFTER">
            <summary> The drafter configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_PROJECT_MANAGEMENT">
            <summary> The project management configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_CONSTRUCTION_MANAGEMENT">
            <summary> The construction management configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_ENGINEERING">
            <summary> The engineering configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_REINFORCED_CONCRETE_DETAILING">
            <summary> The reinforced conrete detailing configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_PRECAST_CONCRETE_DETAILING">
            <summary> The precast concrete detailing configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_STEEL_DETAILING">
            <summary> The steel detailing configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_FULL">
            <summary> The full detailing configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_PRIMARY">
            <summary> The Primary configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_EDUCATIONAL">
            <summary> The educational configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_DEVELOPER">
            <summary> The developer configuration. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_CONSTRUCTION_VIEWER">
            <summary> The construction management configuration restricted to viewer mode. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_CONSTRUCTION_MODELING">
            <summary> The construction management configuration with modeling capabilities. </summary>
        </member>
        <member name="F:Tekla.Structures.ModuleManager.ProgramConfigurationEnum.CONFIGURATION_STEEL_DETAILING_LIMITED">
            <obsolete>Deprecated since 19.0 </obsolete><summary> The old steel detailing limited configuration. This definition was left here to enable old macros in 19.0. </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.dotModuleManagerQuery_t">
            <summary> Serialization structure for ModuleManager querys. </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotModuleManagerQuery_t.aModule">
            <summary> Module string </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotModuleManagerQuery_t.IsPresent">
            <summary> Reply </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotModuleManagerQuery_t.Configuration">
            <summary> Configuration currently running. </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.DotAnalysisModuleManagerQuery">
            <summary> Serialization structure for AnalysisModuleManager querys. </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.DotAnalysisModuleManagerQuery.Module">
            <summary> Module string. </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.DotAnalysisModuleManagerQuery.IsPresent">
            <summary> Reply. </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.Channels">
            <summary>
            The Channels class creates .NET communication channels (currently IPC).
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.Channels.RegisterChannel(System.String)">
            <summary>
            Registers a .NET remoting channel (currently IPC) with the given name.
            </summary>
            <param name="ChannelName">The name of the channel.</param>
            <returns>True on success.</returns>
        </member>
        <member name="T:Tekla.Structures.Internal.FormWrapperFunctionality">
            <exclude/>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke0``1(Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod0{``0})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke1r``2(``0@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1r{``0,``1})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke1v``2(``0,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1v{``0,``1})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke2rr``3(``0@,``1@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rr{``0,``1,``2})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke2rv``3(``0@,``1,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rv{``0,``1,``2})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke2vr``3(``0,``1@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vr{``0,``1,``2})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke2vv``3(``0,``1,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vv{``0,``1,``2})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperFunctionalityBase.Invoke6vvvrrr``7(``0,``1,``2,``3@,``4@,``5@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod6vvvrrr{``0,``1,``2,``3,``4,``5,``6})">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod0`1">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1r`2">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1v`2">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rr`3">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rv`3">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vr`3">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vv`3">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod6vvvrrr`7">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke0``1(Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod0{``0})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke1r``2(``0@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1r{``0,``1})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke1v``2(``0,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1v{``0,``1})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke2rr``3(``0@,``1@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rr{``0,``1,``2})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke2rv``3(``0@,``1,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rv{``0,``1,``2})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke2vr``3(``0,``1@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vr{``0,``1,``2})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke2vv``3(``0,``1,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vv{``0,``1,``2})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.Invoke6vvvrrr``7(``0,``1,``2,``3@,``4@,``5@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod6vvvrrr{``0,``1,``2,``3,``4,``5,``6})">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Internal.FormWrapperFunctionality.WaitAsyncOperation(System.IAsyncResult)">
            <exclude/>
        </member>
        <member name="P:Tekla.Structures.Internal.FormWrapperFunctionality.RequiresMessagePolling">
            <exclude/>
        </member>
        <member name="T:Tekla.Structures.Internal.RemotingProxyHelper">
             <summary>
             Helper class for remoting proxies that serve activated client type objects as singleton instances.
             </summary>
             <example>This is an example implementation of the remoting proxy for RemoteType type.
             <code>
             using System;
             using Tekla.Structures.Internal;
            
             namespace Tekla.Structures.MyProject
             {
                 public interface IRemoteType
                 {
                     // Implementation omitted
                 }
            
                 public sealed class RemoteType : MarshalByRefObject, IRemoteType
                 {
                     // Implementation omitted
                 }
            
                 public sealed class RemoteTypeProxy
                 {
                     private static IRemoteType _Instance;
            
                     static RemoteTypeProxy()
                     {
                         string appUrl = "ipc://ApplicationName";
                         RemoteTypeProxy._Instance = RemotingProxyHelper.CreateInstance&lt;RemoteType&gt;(appUrl);
                     }
            
                     public static IRemoteType Instance
                     {
                         get
                         {
                             RemotingProxyHelper.TestInstance(RemoteTypeProxy._Instance);
                             return RemoteTypeProxy._Instance;
                         }
                     }
                 }
             }
             </code>
             </example>
        </member>
        <member name="M:Tekla.Structures.Internal.RemotingProxyHelper.IsFormWrapperRequired">
            <summary>
            Checks if the FormWrapperFunctionality needs to be used with the current process and thread.
            </summary>
            <returns>true if FormWrapperFunctionality is required; otherwise false.</returns>
        </member>
        <member name="M:Tekla.Structures.Internal.RemotingProxyHelper.SetRemotingProxyFactory(Tekla.Structures.Internal.RemotingProxyFactory)">
            <summary>
            Do not remove or rename!
            </summary>
            <param name="Factory">Factory</param>
        </member>
        <member name="M:Tekla.Structures.Internal.RemotingProxyHelper.CreateInstance``1(System.String)">
             <summary>
             Creates an instance of the remote object. If necessary the object type is
             registered as activated client type before it is created. The created instance
             is also sponsored if necessary.
            
             This method should be called only once per type. Subsequent calls may throw an exception.
             </summary>
             <param name="appUrl">
             URL of the application where this type is activated.
             </param>
        </member>
        <member name="M:Tekla.Structures.Internal.RemotingProxyHelper.TestInstance(System.Object)">
            <summary>
            Checks that the type of the given instance has not been registered as
            activated client type after the instance was instantiated.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.RemotingProxyHelper.InitializeChannels">
            <summary>
            Instantiates and registers client and server channels for remoting.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.RemotingProxyFactory">
            <summary>
            Factory used to instantiate remote proxies correctly in default application domain.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.RemotingProxyFactory.CreateRemotingProxy(System.String,System.String)">
            <summary>
            Instantiates given remoting proxy type from given assembly.
            </summary>
            <param name="AssemblyName">Assembly from which proxy should be loaded</param>
            <param name="TypeName">Proxy type name</param>
            <returns>Proxy object</returns>
        </member>
        <member name="T:Tekla.Structures.Internal.SynchronizeInvokeFunctionality">
            <summary>
            
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.#ctor(System.ComponentModel.ISynchronizeInvoke)">
            <summary>
            
            </summary>
            <param name="Instance"></param>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke0``1(Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod0{``0})">
            <summary>
            
            </summary>
            <typeparam name="TResult"></typeparam>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke1r``2(``0@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1r{``0,``1})">
            <summary>
            
            </summary>
            <typeparam name="T0"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="P0"></param>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke1v``2(``0,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod1v{``0,``1})">
            <summary>
            
            </summary>
            <typeparam name="T0"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="P0"></param>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke2rr``3(``0@,``1@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rr{``0,``1,``2})">
            <summary>
            
            </summary>
            <typeparam name="T0"></typeparam>
            <typeparam name="T1"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="P0"></param>
            <param name="P1"></param>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke2rv``3(``0@,``1,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2rv{``0,``1,``2})">
            <summary>
            
            </summary>
            <typeparam name="T0"></typeparam>
            <typeparam name="T1"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="P0"></param>
            <param name="P1"></param>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke2vr``3(``0,``1@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vr{``0,``1,``2})">
            <summary>
            
            </summary>
            <typeparam name="T0"></typeparam>
            <typeparam name="T1"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="P0"></param>
            <param name="P1"></param>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke2vv``3(``0,``1,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod2vv{``0,``1,``2})">
            <summary>
            
            </summary>
            <typeparam name="T0"></typeparam>
            <typeparam name="T1"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="P0"></param>
            <param name="P1"></param>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Internal.SynchronizeInvokeFunctionality.Invoke6vvvrrr``7(``0,``1,``2,``3@,``4@,``5@,Tekla.Structures.Internal.WrapperFunctionalityBase.TargetMethod6vvvrrr{``0,``1,``2,``3,``4,``5,``6})">
            <summary> </summary>
            <typeparam name="T0"></typeparam>
            <typeparam name="T1"></typeparam>
            <typeparam name="T2"></typeparam>
            <typeparam name="T3"></typeparam>
            <typeparam name="T4"></typeparam>
            <typeparam name="T5"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="P0"></param>
            <param name="P1"></param>
            <param name="P2"></param>
            <param name="P3"></param>
            <param name="P4"></param>
            <param name="P5"></param>
            <param name="Method"></param>
            <returns></returns>
        </member>
        <member name="T:Tekla.Structures.Internal.WrapperDelegateHelper">
            <summary>
            Helper class to create delegates
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.WrapperDelegateHelper.CreateDelegate``1(System.Object,System.String)">
            <summary>
            Creates a delegate of the specified type.
            </summary>
            <typeparam name="T">The type of delegate to create.</typeparam>
            <param name="Instance">The class instance on which the method is invoked.</param>
            <param name="MethodName">The name of the Instance method that the delegate is to present.</param>
            <returns>A delegate of the specified type that represents the specified method.</returns>
        </member>
        <member name="T:Tekla.Structures.PositionTypeEnum">
            <summary>
            The position type for connections and details.
            </summary>
        </member>
        <member name="F:Tekla.Structures.PositionTypeEnum.MIDDLE_PLANE">
            <summary>
            The middle plane position type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.PositionTypeEnum.BOX_PLANE">
            <summary>
            The box plane position type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.PositionTypeEnum.COLLISION_PLANE">
            <summary>
            The collision plane position type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.PositionTypeEnum.END_END_PLANE">
            <summary>
            The end plane position type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.PositionTypeEnum.GUSSET_PLANE">
            <summary>
            The gusset plane position type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DetailTypeEnum">
            <summary>
            The detail type defines what kind of a detail is in question.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DetailTypeEnum.END">
            <summary>
            With the detail type end, the X-axis is oriented towards the part's center point.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DetailTypeEnum.INTERMEDIATE">
            <summary>
            With the detail type intermediate, the X-axis is oriented towards the end point of the part.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DetailTypeEnum.INTERMEDIATE_REVERSE">
            <summary>
            With the detail type intermediate reverse, the X-axis is oriented towards the start point of the part.
            </summary>
        </member>
        <member name="T:Tekla.Structures.AutoDirectionTypeEnum">
            <summary>
            The auto direction type defines how a connection or detail coordinate system will be oriented automatically.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_NA">
            <summary>
            The auto direction will not be available.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_BASIC">
            <summary>
            The auto direction type for usual joints will be used.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_DIAGONAL">
            <summary>
            The auto direction type for diagonal joints will be used.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_SPLICE">
            <summary>
            The auto direction type for splices will be used.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_DETAIL">
            <summary>
            The auto direction type for details will be used.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_GLOBAL_Z">
            <summary>
            The auto direction type with joint direction at the global Z will be used.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_SEATING">
            <summary>
            The auto direction type for seating joints will be used.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_PRIMARY_X">
            <summary>
            The auto direction type with joint direction parallel to the primary X-axis will be used.
            </summary>
        </member>
        <member name="F:Tekla.Structures.AutoDirectionTypeEnum.AUTODIR_FROM_ATTRIBUTE_FILE">
            <summary>
            The auto direction type is fetched from the loaded attribute file.
            </summary>
        </member>
        <member name="T:Tekla.Structures.ConnectionStatusEnum">
            <summary>
            The status type defines what the status of a connection or a detail is.
            In the model the color of the symbol (green, yellow, red) indicates the status.
            </summary>
        </member>
        <member name="F:Tekla.Structures.ConnectionStatusEnum.STATUS_UNKNOWN">
            <summary>
            The status is unknown.
            </summary>
        </member>
        <member name="F:Tekla.Structures.ConnectionStatusEnum.STATUS_OK">
            <summary>
            The status is ok and the symbol color is green.
            </summary>
        </member>
        <member name="F:Tekla.Structures.ConnectionStatusEnum.STATUS_WARNING">
            <summary>
            The status indicates a warning and the symbol color is yellow.
            </summary>
        </member>
        <member name="F:Tekla.Structures.ConnectionStatusEnum.STATUS_ERROR">
            <summary>
            The status indicates an error and the symbol color is red.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.AABB">
            <summary>
            The AABB class represents an axis-aligned 3d bounding box.
            </summary>
            <example>
            <code>
            using Tekla.Structures.Geometry3d;
            using Tekla.Structures.Model;
            using System;
            
            public class Example
            {
                   public void Example1()
                   {
                       Beam MyBeam1 = new Beam();
                       Beam MyBeam2 = new Beam();
            
                       Solid MySolid1 = MyBeam1.GetSolid();
                       Solid MySolid2 = MyBeam2.GetSolid();
                       AABB MyAxisAlignedBoundingBox1 = new AABB(MySolid1.MinimumPoint, MySolid1.MaximumPoint);
                       AABB MyAxisAlignedBoundingBox2 = new AABB(MySolid2.MinimumPoint, MySolid2.MaximumPoint);
            
                       if (MyAxisAlignedBoundingBox1.Collide(MyAxisAlignedBoundingBox2))
                       {
                           Console.WriteLine("Collision between beams!");
                       }
                   }
            }
            </code>
            </example>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.IBoundingVolume">
            <summary>
            The BoundingVolume interface represents any generic 3D bounding volume.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.#ctor">
            <summary>
            Instantiates a new axis-aligned 3d bounding box with the maximum point
            initialized to the smallest possible value and the minimum point to the
            largest possible value.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.#ctor(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Instantiates a new axis-aligned 3d bounding box with the given minimum and maximum points.
            </summary>
            <param name="MinPoint">The minimum point to be used.</param>
            <param name="MaxPoint">The maximum point to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.#ctor(Tekla.Structures.Geometry3d.AABB)">
            <summary>
            Instantiates a new axis-aligned 3d bounding box which is a copy
            of the given axis-aligned 3d bounding box.
            </summary>
            <param name="AABB">The axis-aligned 3d bounding box to copy from.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.IsInside(Tekla.Structures.Geometry3d.Point)">
            <summary>
            Checks if the given point is inside the current axis-aligned 3d bounding box.
            </summary>
            <param name="Point">The point to be checked.</param>
            <returns>True if the point is inside the current axis-aligned 3d bounding box.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.IsInside(Tekla.Structures.Geometry3d.LineSegment)">
            <summary>
            Checks if the given line segment is inside the current axis-aligned 3d bounding box.
            </summary>
            <param name="LineSegment">The line segment to be checked.</param>
            <returns>True if the point is inside the current axis-aligned 3d bounding box.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.GetCenterPoint">
            <summary>
            Returns the geometric center point of the current axis-aligned 3d bounding box.
            </summary>
            <returns>The geometric center point of the current axis-aligned 3d bounding box.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.Collide(Tekla.Structures.Geometry3d.AABB)">
            <summary>
            Checks if the current axis-aligned 3d bounding box collides with
            another given axis-aligned 3d bounding box. Both axis-aligned 3d
            bounding boxes need to be in the same coordinate system or in the
            same workplane, so that they are defined using the same axes.
            </summary>
            <param name="Other">Another axis-aligned 3d bounding box to be used in the collision check.</param>
            <returns>True if the axis-aligned 3d bounding boxes collide.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.op_Addition(Tekla.Structures.Geometry3d.AABB,Tekla.Structures.Geometry3d.AABB)">
            <summary>
            Combines (adds) the given two axis-aligned 3d bounding boxes.
            </summary>
            <param name="AABB1">The first axis-aligned 3d bounding box to combine.</param>
            <param name="AABB2">The second axis-aligned 3d bounding box to combine.</param>
            <returns>The new combined axis-aligned 3d bounding box.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.op_Addition(Tekla.Structures.Geometry3d.AABB,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Adds the given point to the given axis-aligned 3d bounding box.
            </summary>
            <param name="AABB">The axis-aligned 3d bounding box to add to.</param>
            <param name="Point">The point to be added.</param>
            <returns>The new axis-aligned 3d bounding box which includes the
            given axis-aligned 3d bounding box and the given point.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.AABB.op_Addition(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.AABB)">
            <summary>
            Adds the given point to the given axis-aligned 3d bounding box.
            </summary>
            <param name="Point">The point to be added.</param>
            <param name="AABB">The axis-aligned 3d bounding box to add to.</param>
            <returns>The new axis-aligned 3d bounding box which includes the given
            axis-aligned 3d bounding box and the given point.</returns>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.AABB.MinPoint">
            <summary>
            The minimum point of the axis-aligned 3d bounding box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.AABB.MaxPoint">
            <summary>
            The maximum point of the axis-aligned 3d bounding box.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.GeometryConstants">
            <summary>
            The Constants class of Geometry3d holds certain constant values that are used
            internally by the other geometry classes.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.GeometryConstants.DISTANCE_EPSILON">
            <summary>
            The minimum distance used for checking if points, lines, etc. coincide.
            Anything smaller will be considered equivalent to 0.0.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.GeometryConstants.ANGULAR_EPSILON">
            <summary>
            The minimum angle (radians) used for checking angular parallelism, perpendicularity, etc.
            Anything smaller will be considered equivalent to 0.0.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.GeometryConstants.SCALAR_EPSILON">
            <summary>
            The minimum value used for comparing floating point scalar values. Any smaller difference will be considered
            equivalent to 0.0.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.CoordinateSystem">
            <summary>
            The CoordinateSystem class defines a coordinate system in space. The system is
            defined by an origin, an X-axis and a Y-axis. The Z-axis is the cross product of the X-axis and the Y-axis.
            </summary>
            <example>
            The following example creates a new coordinate system which is used to create a new view.
            <code>
            using System;
            using Tekla.Structures.Drawing;
            using Tekla.Structures.Geometry3d;
            
            public class Example
            {
                   public void Example1()
                   {
                       Drawing GADrawing = new GADrawing();
                       CoordinateSystem CoordinateSystem = new CoordinateSystem(new Point(), new Vector(1, 0, 0), new Vector(0, 1, 0));
                       View newView = new View(GADrawing.GetSheet(), CoordinateSystem, CoordinateSystem, new AABB(new Point(0, 0), new Point(1000, 1000, 1000)));
                   }
            }
            </code>
            </example>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.CoordinateSystem._Origin">
            <summary>
            The origin of the coordinate system.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.CoordinateSystem._AxisX">
            <summary>
            The X-axis of the coordinate system.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.CoordinateSystem._AxisY">
            <summary>
            The Y-axis of the coordinate system.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.CoordinateSystem.#ctor">
            <summary>
            Instantiates a coordinate system in the current work plane.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.CoordinateSystem.#ctor(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Instantiates a coordinate system with the given origin, X-axis and Y-axis.
            </summary>
            <param name="Origin">The origin for the coordinate system.</param>
            <param name="AxisX">The X-axis for the coordinate system.</param>
            <param name="AxisY">The Y-axis for the coordinate system.</param>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.CoordinateSystem.Origin">
            <summary>
            The origin of the coordinate system.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.CoordinateSystem.AxisX">
            <summary>
            The X-axis of the coordinate system.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.CoordinateSystem.AxisY">
            <summary>
            The Y-axis of the coordinate system.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.dotCoordinateSystem_t">
            <summary>
            The serialization structure for CoordinateSystem.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotCoordinateSystem_t.Origin">
            <summary>
            The origin of the coordinate system.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotCoordinateSystem_t.AxisX">
            <summary>
            The X-Axis of the coordinate system.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotCoordinateSystem_t.AxisY">
            <summary>
            The Y-Axis of the coordinate system.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.dotCoordinateSystem_t.ToStruct(Tekla.Structures.Geometry3d.CoordinateSystem)">
            <summary>
            CoordinateSystem to dotCoordinateSystem_t.
            </summary>
            <param name="P"></param>
        </member>
        <member name="M:Tekla.Structures.Internal.dotCoordinateSystem_t.FromStruct(Tekla.Structures.Geometry3d.CoordinateSystem)">
            <summary>
            
            </summary>
            <param name="P"></param>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Distance">
            <summary>
            The Distance class contains methods for calculating the distance between geometric objects.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Distance.PointToPoint(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Returns the distance between the given two points.
            </summary>
            <param name="Point1">The first point to be used.</param>
            <param name="Point2">The second point to be used.</param>
            <returns>The distance between the given points.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Distance.PointToLine(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Line)">
            <summary>
            Returns the distance between the given point and line.
            </summary>
            <param name="Point">The point to be used.</param>
            <param name="Line">The line to be used.</param>
            <returns>The distance between the given point and line.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Distance.PointToLineSegment(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.LineSegment)">
            <summary>
            Returns the distance between the given point and line segment.
            </summary>
            <param name="Point">The point to be used.</param>
            <param name="LineSegment">The line segment to be used.</param>
            <returns>The distance between the given point and line segment.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Distance.PointToPlane(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns the distance between the given point and plane.
            </summary>
            <param name="Point">The point to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>The distance between the given point and plane.</returns>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.GeometricPlane">
            <summary>
            The GeometricPlane class represents a 3d geometric plane.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.GeometricPlane.#ctor">
            <summary>
            Instantiates an XY-plane with the origin at (0,0,0).
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.GeometricPlane.#ctor(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Instantiates a plane defined by the given origin point and normal vector.
            </summary>
            <param name="Origin">The origin point to be used.</param>
            <param name="Normal">The normal vector to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.GeometricPlane.#ctor(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Instantiates a plane defined by the given origin, X-axis vector and Y-axis vector.
            </summary>
            <param name="Origin">The origin to be used.</param>
            <param name="Xaxis">The X-axis to be used.</param>
            <param name="Yaxis">The Y-axis to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.GeometricPlane.#ctor(Tekla.Structures.Geometry3d.CoordinateSystem)">
            <summary>
            Instantiates a plane defined by the given coordinate system.
            </summary>
            <param name="CoordSys">The coordinate system to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.GeometricPlane.GetNormal">
            <summary>
            Returns a normalized normal vector of the plane.
            </summary>
            <returns>The normalized normal vector of the plane.</returns>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.GeometricPlane.Origin">
            <summary>
            The origin point of the plane.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.GeometricPlane.Normal">
            <summary>
            The normal vector of the plane.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Intersection">
            <summary>
            The Intersection class contains methods for calculating intersections between geometric objects.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Intersection.LineToLine(Tekla.Structures.Geometry3d.Line,Tekla.Structures.Geometry3d.Line)">
            <summary>
            Returns a new line segment which is the shortest path between the given lines or null if the lines are
            parallel. If the resulting line segment has a length of 0.0, the given lines actually intersect
            in 3d space.
            </summary>
            <param name="Line1">The first line to be used.</param>
            <param name="Line2">The second line to be used.</param>
            <returns>The shortest line segment between the given lines or null if the lines are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Intersection.LineToPlane(Tekla.Structures.Geometry3d.Line,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns a new point which is an intersection of the given line and plane or null if the line and the plane are
            parallel.
            </summary>
            <param name="Line">The line to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>The intersection point or null if no intersection was found.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Intersection.LineSegmentToPlane(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns a new point which is an intersection of the given line segment and plane or null if
            the line segment and the plane are parallel or do not intersect.
            </summary>
            <param name="LineSegment">The line segment to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>The intersection point or null if no intersection was found.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Intersection.PlaneToPlane(Tekla.Structures.Geometry3d.GeometricPlane,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns a new line which is an intersection of the given two planes or null if the planes are
            parallel.
            </summary>
            <param name="Plane1">The first plane to be used.</param>
            <param name="Plane2">The second plane to be used.</param>
            <returns>The intersection line or null if no intersection was found.</returns>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Line">
            <summary>
            The Line class represents a single infinite line in 3D space. See LineSegment for
            the implementation of a segment of a line.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Line.#ctor">
            <summary>
            Instantiates a line with an undefined direction.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Line.#ctor(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Instantiates a line defined by the given points.
            </summary>
            <param name="p1">The first point to be used.</param>
            <param name="p2">The second point to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Line.#ctor(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Instantiates a line defined by the given point and direction vector.
            </summary>
            <param name="Point">A point that will be on the line.</param>
            <param name="Direction">The direction of the line.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Line.#ctor(Tekla.Structures.Geometry3d.LineSegment)">
            <summary>
            Instantiates a line defined by the given line segment.
            </summary>
            <param name="LineSegment">The line segment to be used.</param>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.Line.Origin">
            <summary>
            The origin of the line.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.Line.Direction">
            <summary>
            The direction vector of the line.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.LineSegment">
            <summary>
            The LineSegment class represents a single finite segment of a line in 3D space. See Line
            for the implementation of a straight line.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.#ctor">
            <summary>
            Instantiates a line segment with both the starting point and the end point zeroed.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.#ctor(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Instantiates a line segment with the given points.
            </summary>
            <param name="Point1">The starting point to be used.</param>
            <param name="Point2">The end point to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.Equals(System.Object)">
            <summary>
            Returns true if the objects are equal.
            </summary>
            <param name="o">The object that equality is wished to be checked with.</param>
            <returns>True if the objects are equal.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.Length">
            <summary>
            Returns the length of a line segment.
            </summary>
            <returns>The length of the line segment.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.GetDirectionVector">
            <summary>
            Returns a new unit direction vector of a line segment.
            </summary>
            <returns>The unit direction vector of the line segment.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.GetHashCode">
            <summary>
            Returns a hash code for a line segment.
            Notice, in extremely rare cases, you might not get the same
            hash code for two line segments even though they are considered equal! This 
            should, however, happen only in extremely rare cases!
            </summary>
            <returns>The hash code for the line segment.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.op_Equality(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.LineSegment)">
            <summary>
            Checks the equality of two line segments.
            </summary>
            <param name="Segment1">The first line segment to be used.</param>
            <param name="Segment2">The second line segment to be used.</param>
            <returns>True if the two line segments are equal.
            False otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.LineSegment.op_Inequality(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.LineSegment)">
            <summary>
            Checks the inequality of two line segments.
            </summary>
            <param name="Segment1">The first line segment to be used.</param>
            <param name="Segment2">The second line segment to be used.</param>
            <returns>True if the two line segments are not equal.
            False otherwise.</returns>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.LineSegment.Point1">
            <summary>
            The starting point of the line segment.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.LineSegment.Point2">
            <summary>
            The end point of the line segment.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Matrix">
            <summary>
            The Matrix class represents a 4x3 matrix.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.Matrix.Transformation">
            <summary>
            The transformation.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.#ctor">
            <summary>
            Creates a new indentity matrix.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.#ctor(Tekla.Structures.Geometry3d.Matrix)">
            <summary>
            Creates a new matrix which is a copy of the given matrix.
            </summary>
            <param name="m">The matrix to copy from.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.Transpose">
            <summary>
            Transposes a matrix. The resulting matrix is an inversion of the current matrix,
            if the current matrix was a valid rotation matrix.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.GetTranspose">
            <summary>
            Returns a new matrix which is a transpose of the current matrix.
            The transposed matrix is an inversion of the current matrix,
            if the current matrix was a valid rotation matrix.
            </summary>
            <returns>The new transposed matrix.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.op_Multiply(Tekla.Structures.Geometry3d.Matrix,Tekla.Structures.Geometry3d.Matrix)">
            <summary>
            Multiplies (combines) two transformation matrices.
            </summary>
            <param name="B">The transformation matrix which is applied second.</param>
            <param name="A">The transformation matrix which is applied first.</param>
            <returns>The new compound transformation matrix.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.Transform(Tekla.Structures.Geometry3d.Point)">
            <summary>
            Transforms the given point using the current matrix.
            </summary>
            <param name="p">The point to be transformed.</param>
            <returns>The new transformed point.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.op_Multiply(Tekla.Structures.Geometry3d.Matrix,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Transforms the given point using the given matrix.
            </summary>
            <param name="A">The transformation matrix.</param>
            <param name="p">The point to be transformed.</param>
            <returns>The new transformed point.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Matrix.ToString">
            <summary>
            Returns a string that represents the current matrix.
            </summary>
            <returns>The string that represents the current matrix.</returns>
        </member>
        <member name="P:Tekla.Structures.Geometry3d.Matrix.Item(System.Int32,System.Int32)">
            <summary>
            Sets or gets the matrix element values.
            </summary>
            <param name="row">The matrix row index (0-3).</param>
            <param name="column">The matrix column index (0-2).</param>
            <returns>The value at the specified index.</returns>
        </member>
        <member name="T:Tekla.Structures.Internal.dotMatrix_t">
            <summary>
            Serialization structure for TransformationPlane.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotMatrix_t.aMatrix">
            <summary>
            Matrix4x3 as an array.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.dotMatrix_t.#ctor(System.Int32)">
            <summary>
            Creates a new dotMatrix_t instance.
            </summary>
            <param name="Size">4x3 elements.</param>
        </member>
        <member name="M:Tekla.Structures.Internal.dotMatrix_t.ToStruct(Tekla.Structures.Geometry3d.Matrix)">
            <summary>
            Matrix4x3 to struct.
            </summary>
            <param name="Matrix">Matrix4x3.</param>
        </member>
        <member name="M:Tekla.Structures.Internal.dotMatrix_t.FromStruct(Tekla.Structures.Geometry3d.Matrix)">
            <summary>
            Matrix4x3 from struct.
            </summary>
            <param name="Matrix">Matrix4x3.</param>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.MatrixFactory">
            <summary>
            The MatrixFactory class provides a convenient way to generate different
            kinds of transformation matrices.
            </summary>
            <example>
            The matrix factory can be used to create some useful transformation
            matrices between different coordinate systems:
            <code>
            using Tekla.Structures.Geometry3d;
            using Tekla.Structures.Model;
            
            public class Example
            {
                   public void Example1()
                   {
                       Beam Beam1 = new Beam();
                       Beam Beam2 = new Beam();
                       Point Point1 = new Point();
            
                       CoordinateSystem Csys1 = Beam1.GetCoordinateSystem();
                       CoordinateSystem Csys2 = Beam2.GetCoordinateSystem();
            
                       Matrix Matrix = MatrixFactory.ByCoordinateSystems(Csys1, Csys2);
                       Point Point2 = Matrix.Transform(Point1);
            
                       // The same result for Point2 when using two separate transformations
                       Matrix ToCurrentWP = MatrixFactory.FromCoordinateSystem(Csys1);
                       Point CurrentPoint = ToCurrentWP.Transform(Point1);
            
                       Matrix ToLocal = MatrixFactory.ToCoordinateSystem(Csys2);
                       Point2 = ToLocal.Transform(CurrentPoint);
                   }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.MatrixFactory.Rotate(System.Double,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Returns a rotation matrix defined by the given angle
            and the given rotation axis.
            </summary>
            <param name="Angle">The rotation angle (in radians).</param>
            <param name="Axis">The rotation axis.</param>
            <returns>The new rotation matrix.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.MatrixFactory.ToCoordinateSystem(Tekla.Structures.Geometry3d.CoordinateSystem)">
             <summary>
             Returns a coordinate transformation matrix defined by the given coordinate system.
             With the returned matrix points can be transformed from the current work plane coordinate system
             to the given coordinate system.
             </summary>
             <param name="CoordSys">The coordinate system to transform points to.</param>
             <returns>The transformation matrix defined by the given coordinate system.</returns>
             <example>This example shows the extremes of the beam in its local coordinate system.
             <code>
             using System;
             using System.Globalization;
             using Tekla.Structures.Geometry3d;
             using Tekla.Structures.Model;
             using Tekla.Structures.Model.UI;
             
             public class Example
             {
                    private readonly Model _Model = new Model();
                    private static GraphicsDrawer GraphicsDrawer = new GraphicsDrawer();
                    private readonly static Color TextColor = new Color(1, 0, 1);
            
                    //Shows the beam's extremes in its local coordinates
                    private static void ShowExtremesInBeamLocalCoordinates(Beam Beam)
                    {
                        //Get matrix to transform points to the beam's local coordinate system
                        Matrix TransformationMatrix = MatrixFactory.ToCoordinateSystem(Beam.GetCoordinateSystem());
            
                        //Transform the points from current work plane to the local coordinate system
                        Point LocalStartPoint = TransformationMatrix.Transform(Beam.StartPoint);
                        Point LocalEndPoint = TransformationMatrix.Transform(Beam.EndPoint);
            
                        //Display results
                        DrawCoordinateSytem(Beam.GetCoordinateSystem());
                        GraphicsDrawer.DrawText(Beam.StartPoint, FormatPointCoordinates(LocalStartPoint), TextColor);
                        GraphicsDrawer.DrawText(Beam.EndPoint, FormatPointCoordinates(LocalEndPoint), TextColor);
                    }
             
                    //Draws the coordinate system in which the values are shown
                    private static void DrawCoordinateSytem(CoordinateSystem CoordinateSystem)
                    {
                        DrawVector(CoordinateSystem.Origin, CoordinateSystem.AxisX, "X");
                        DrawVector(CoordinateSystem.Origin, CoordinateSystem.AxisY, "Y");
                    }
            
                    //Draws the vector of the coordinate system
                    private static void DrawVector(Point StartPoint, Vector Vector, string Text)
                    {
                        Color Color = new Color(0, 1, 1);
                        const double Radians = 0.43;
            
                        Vector = Vector.GetNormal();
                        Vector Arrow01 = new Vector(Vector);
            
                        Vector.Normalize(500);
                        Point EndPoint = new Point(StartPoint);
                        EndPoint.Translate(Vector.X, Vector.Y, Vector.Z);
                        GraphicsDrawer.DrawLineSegment(StartPoint, EndPoint, Color);
            
                        GraphicsDrawer.DrawText(EndPoint, Text, Color);
            
                        Arrow01.Normalize(-100);
                        Vector Arrow = ArrowVector(Arrow01, Radians);
            
                        Point ArrowExtreme = new Point(EndPoint);
                        ArrowExtreme.Translate(Arrow.X, Arrow.Y, Arrow.Z);
                        GraphicsDrawer.DrawLineSegment(EndPoint, ArrowExtreme, Color);
            
                        Arrow = ArrowVector(Arrow01, -Radians);
            
                        ArrowExtreme = new Point(EndPoint);
                        ArrowExtreme.Translate(Arrow.X, Arrow.Y, Arrow.Z);
                        GraphicsDrawer.DrawLineSegment(EndPoint, ArrowExtreme, Color);
                    }
            
                    //Draws the arrows of the vectors
                    private static Vector ArrowVector(Vector Vector, double Radians)
                    {
                        double X, Y, Z;
            
                        if(Vector.X == 0 &amp;&amp; Vector.Y == 0)
                        {
                            X = Vector.X;
                            Y = (Vector.Y * Math.Cos(Radians)) - (Vector.Z * Math.Sin(Radians));
                            Z = (Vector.Y * Math.Sin(Radians)) + (Vector.Z * Math.Cos(Radians));
                        }
                        else
                        {
                            X = (Vector.X * Math.Cos(Radians)) - (Vector.Y * Math.Sin(Radians));
                            Y = (Vector.X * Math.Sin(Radians)) + (Vector.Y * Math.Cos(Radians));
                            Z = Vector.Z;
                        }
            
                        return new Vector(X, Y, Z);
                    }
            
                    //Shows the point coordinates with only two decimals
                    private static string FormatPointCoordinates(Point Point)
                    {
                        string Output = String.Empty;
            
                        Output = "(" + Point.X.ToString("0.00", CultureInfo.InvariantCulture) + ", " +
                                 Point.Y.ToString("0.00", CultureInfo.InvariantCulture) + ", " +
                                 Point.Z.ToString("0.00", CultureInfo.InvariantCulture) + ")";
            
                        return Output;
                    }
             }
             </code>
             </example>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.MatrixFactory.FromCoordinateSystem(Tekla.Structures.Geometry3d.CoordinateSystem)">
             <summary>
             Returns a coordinate transformation matrix defined by the given coordinate system.
             With the returned matrix points can be transformed from the given coordinate system to
             the current work plane coordinate system.
             </summary>
             <param name="CoordSys">The coordinate system to transform points from.</param>
             <returns>The transformation matrix defined by the given coordinate system.</returns>
             <example>This example shows the extremes of the beam in the current work plane coordinate system.
             <code>
             using System;
             using System.Globalization;
             using Tekla.Structures.Geometry3d;
             using Tekla.Structures.Model;
             using Tekla.Structures.Model.UI;
             
             public class Example
             {
                    private readonly Model _Model = new Model();
                    private static GraphicsDrawer GraphicsDrawer = new GraphicsDrawer();
                    private readonly static Color TextColor = new Color(1, 0, 1);
            
                    //Shows the beam's extremes in the current work plane coordinates
                    private void ShowExtremesInCurrentCoordinates(Beam Beam)
                    {
                        //Set the transformation plane to be in another location than global
                        //Comment these lines out if you have done that already
                        TransformationPlane CurrentTP = _Model.GetWorkPlaneHandler().GetCurrentTransformationPlane();
                        _Model.GetWorkPlaneHandler().SetCurrentTransformationPlane(new TransformationPlane(Beam.GetCoordinateSystem()));
            
                        //Update the beam's extremes to the new transformation plane
                        Beam.Select();
                        Point LocalStartPoint = Beam.StartPoint;
                        Point LocalEndPoint = Beam.EndPoint;
            
                        _Model.GetWorkPlaneHandler().SetCurrentTransformationPlane(CurrentTP);
            
                        //Get the matrix to transform the coordinates from local to current
                        Matrix TransformationMatrix = MatrixFactory.FromCoordinateSystem(Beam.GetCoordinateSystem());
            
                        //Transform the points from local to the current work plane coordinate system
                        Point CurrentStartPoint = TransformationMatrix.Transform(LocalStartPoint);
                        Point CurrentEndPoint = TransformationMatrix.Transform(LocalEndPoint);
            
                        //Display results
                        DrawCoordinateSytem(new CoordinateSystem());
                        GraphicsDrawer.DrawText(CurrentStartPoint, FormatPointCoordinates(CurrentStartPoint), TextColor);
                        GraphicsDrawer.DrawText(CurrentEndPoint, FormatPointCoordinates(CurrentEndPoint), TextColor);
                    }
             
                    //Draws the coordinate system in which the values are shown
                    private static void DrawCoordinateSytem(CoordinateSystem CoordinateSystem)
                    {
                        DrawVector(CoordinateSystem.Origin, CoordinateSystem.AxisX, "X");
                        DrawVector(CoordinateSystem.Origin, CoordinateSystem.AxisY, "Y");
                    }
            
                    //Draws the vector of the coordinate system
                    private static void DrawVector(Point StartPoint, Vector Vector, string Text)
                    {
                        Color Color = new Color(0, 1, 1);
                        const double Radians = 0.43;
            
                        Vector = Vector.GetNormal();
                        Vector Arrow01 = new Vector(Vector);
            
                        Vector.Normalize(500);
                        Point EndPoint = new Point(StartPoint);
                        EndPoint.Translate(Vector.X, Vector.Y, Vector.Z);
                        GraphicsDrawer.DrawLineSegment(StartPoint, EndPoint, Color);
            
                        GraphicsDrawer.DrawText(EndPoint, Text, Color);
            
                        Arrow01.Normalize(-100);
                        Vector Arrow = ArrowVector(Arrow01, Radians);
            
                        Point ArrowExtreme = new Point(EndPoint);
                        ArrowExtreme.Translate(Arrow.X, Arrow.Y, Arrow.Z);
                        GraphicsDrawer.DrawLineSegment(EndPoint, ArrowExtreme, Color);
            
                        Arrow = ArrowVector(Arrow01, -Radians);
            
                        ArrowExtreme = new Point(EndPoint);
                        ArrowExtreme.Translate(Arrow.X, Arrow.Y, Arrow.Z);
                        GraphicsDrawer.DrawLineSegment(EndPoint, ArrowExtreme, Color);
                    }
            
                    //Draws the arrows of the vectors
                    private static Vector ArrowVector(Vector Vector, double Radians)
                    {
                        double X, Y, Z;
            
                        if(Vector.X == 0 &amp;&amp; Vector.Y == 0)
                        {
                            X = Vector.X;
                            Y = (Vector.Y * Math.Cos(Radians)) - (Vector.Z * Math.Sin(Radians));
                            Z = (Vector.Y * Math.Sin(Radians)) + (Vector.Z * Math.Cos(Radians));
                        }
                        else
                        {
                            X = (Vector.X * Math.Cos(Radians)) - (Vector.Y * Math.Sin(Radians));
                            Y = (Vector.X * Math.Sin(Radians)) + (Vector.Y * Math.Cos(Radians));
                            Z = Vector.Z;
                        }
            
                        return new Vector(X, Y, Z);
                    }
            
                    //Shows the point coordinates with only two decimals
                    private static string FormatPointCoordinates(Point Point)
                    {
                        string Output = String.Empty;
            
                        Output = "(" + Point.X.ToString("0.00", CultureInfo.InvariantCulture) + ", " +
                                 Point.Y.ToString("0.00", CultureInfo.InvariantCulture) + ", " +
                                 Point.Z.ToString("0.00", CultureInfo.InvariantCulture) + ")";
            
                        return Output;
                    }
             }
             </code>
             </example>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.MatrixFactory.ByCoordinateSystems(Tekla.Structures.Geometry3d.CoordinateSystem,Tekla.Structures.Geometry3d.CoordinateSystem)">
             <summary>
             Returns a coordinate transformation matrix defined by two coordinate systems.
             With the returned matrix points can be transformed from the first coordinate system to
             the second coordinate system. The ByCoordinateSystems method is meant for transforming
             points between coordinate systems asked in the same work plane.
             </summary>
             <param name="CoordSys1">The coordinate system to start from.</param>
             <param name="CoordSys2">The target coordinate system.</param>
             <returns>The transformation matrix defined by the two coordinate systems.</returns>
             <example>This example shows the beam's extremes in some other model object's coordinate system.
             <code>
             using System;
             using System.Globalization;
             using Tekla.Structures.Geometry3d;
             using Tekla.Structures.Model;
             using Tekla.Structures.Model.UI;
             
             public class Example
             {
                    private readonly Model _Model = new Model();
                    private static GraphicsDrawer GraphicsDrawer = new GraphicsDrawer();
                    private readonly static Color TextColor = new Color(1, 0, 1);
            
                    //Shows the beam's extremes in the coordinates of the reference model object
                    private void ShowExtremesInOtherObjectCoordinates(ModelObject ReferenceObject, Beam Beam)
                    {
                        //Set the transformation plane to use the beam's coordinate system in order to get the beam's extremes in the local coordinate system
                        TransformationPlane CurrentTP = _Model.GetWorkPlaneHandler().GetCurrentTransformationPlane();
                        _Model.GetWorkPlaneHandler().SetCurrentTransformationPlane(new TransformationPlane(Beam.GetCoordinateSystem()));
            
                        //Update the beam's extremes to the new transformation plane
                        Beam.Select();
                        Point LocalStartPoint = Beam.StartPoint;
                        Point LocalEndPoint = Beam.EndPoint;
            
                        //Get the beam's extremes in the reference object's coordinates
                        Matrix TransformationMatrix = MatrixFactory.ByCoordinateSystems(Beam.GetCoordinateSystem(), ReferenceObject.GetCoordinateSystem());
            
                        //Transform the extreme points to the new coordinate system
                        Point BeamStartPoint = TransformationMatrix.Transform(LocalStartPoint);
                        Point BeamEndPoint = TransformationMatrix.Transform(LocalEndPoint);
            
                        _Model.GetWorkPlaneHandler().SetCurrentTransformationPlane(CurrentTP);
            
                        //Transform the points where to show the texts to current work plane coordinate system
                        Matrix TransformationToCurrent = MatrixFactory.FromCoordinateSystem(ReferenceObject.GetCoordinateSystem());
                        Point BeamStartPointInCurrent = TransformationToCurrent.Transform(BeamStartPoint);
                        Point BeamEndPointInCurrent = TransformationToCurrent.Transform(BeamEndPoint);
            
                        //Display results
                        DrawCoordinateSytem(ReferenceObject.GetCoordinateSystem());
                        GraphicsDrawer.DrawText(BeamStartPointInCurrent, FormatPointCoordinates(BeamStartPoint), TextColor);
                        GraphicsDrawer.DrawText(BeamEndPointInCurrent, FormatPointCoordinates(BeamEndPoint), TextColor);
                    }
             
                    //Draws the coordinate system in which the values are shown
                    private static void DrawCoordinateSytem(CoordinateSystem CoordinateSystem)
                    {
                        DrawVector(CoordinateSystem.Origin, CoordinateSystem.AxisX, "X");
                        DrawVector(CoordinateSystem.Origin, CoordinateSystem.AxisY, "Y");
                    }
            
                    //Draws the vector of the coordinate system
                    private static void DrawVector(Point StartPoint, Vector Vector, string Text)
                    {
                        Color Color = new Color(0, 1, 1);
                        const double Radians = 0.43;
            
                        Vector = Vector.GetNormal();
                        Vector Arrow01 = new Vector(Vector);
            
                        Vector.Normalize(500);
                        Point EndPoint = new Point(StartPoint);
                        EndPoint.Translate(Vector.X, Vector.Y, Vector.Z);
                        GraphicsDrawer.DrawLineSegment(StartPoint, EndPoint, Color);
            
                        GraphicsDrawer.DrawText(EndPoint, Text, Color);
            
                        Arrow01.Normalize(-100);
                        Vector Arrow = ArrowVector(Arrow01, Radians);
            
                        Point ArrowExtreme = new Point(EndPoint);
                        ArrowExtreme.Translate(Arrow.X, Arrow.Y, Arrow.Z);
                        GraphicsDrawer.DrawLineSegment(EndPoint, ArrowExtreme, Color);
            
                        Arrow = ArrowVector(Arrow01, -Radians);
            
                        ArrowExtreme = new Point(EndPoint);
                        ArrowExtreme.Translate(Arrow.X, Arrow.Y, Arrow.Z);
                        GraphicsDrawer.DrawLineSegment(EndPoint, ArrowExtreme, Color);
                    }
            
                    //Draws the arrows of the vectors
                    private static Vector ArrowVector(Vector Vector, double Radians)
                    {
                        double X, Y, Z;
            
                        if(Vector.X == 0 &amp;&amp; Vector.Y == 0)
                        {
                            X = Vector.X;
                            Y = (Vector.Y * Math.Cos(Radians)) - (Vector.Z * Math.Sin(Radians));
                            Z = (Vector.Y * Math.Sin(Radians)) + (Vector.Z * Math.Cos(Radians));
                        }
                        else
                        {
                            X = (Vector.X * Math.Cos(Radians)) - (Vector.Y * Math.Sin(Radians));
                            Y = (Vector.X * Math.Sin(Radians)) + (Vector.Y * Math.Cos(Radians));
                            Z = Vector.Z;
                        }
            
                        return new Vector(X, Y, Z);
                    }
            
                    //Shows the point coordinates with only two decimals
                    private static string FormatPointCoordinates(Point Point)
                    {
                        string Output = String.Empty;
            
                        Output = "(" + Point.X.ToString("0.00", CultureInfo.InvariantCulture) + ", " +
                                 Point.Y.ToString("0.00", CultureInfo.InvariantCulture) + ", " +
                                 Point.Z.ToString("0.00", CultureInfo.InvariantCulture) + ")";
            
                        return Output;
                    }
             }
             </code>
             </example>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Parallel">
            <summary>
            The Parallel class contains methods for testing the parallelism of geometric objects.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.VectorToVector(Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Returns true if the given vectors are parallel.
            </summary>
            <param name="Vector1">The first vector to be used.</param>
            <param name="Vector2">The second vector to be used.</param>
            <returns>True if the given vectors are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.VectorToVector(Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.Vector,System.Double)">
            <summary>
            Returns true if the given vectors are parallel within the given angular tolerance.
            </summary>
            <param name="Vector1">The first vector to be used.</param>
            <param name="Vector2">The second vector to be used.</param>
            <param name="Tolerance">The angular tolerance (in radians) to be used.</param>
            <returns>True if the given vectors are parallel within the given tolerance.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineToLine(Tekla.Structures.Geometry3d.Line,Tekla.Structures.Geometry3d.Line)">
            <summary>
            Returns true if the given lines are parallel.
            </summary>
            <param name="Line1">The first line to be used.</param>
            <param name="Line2">The second line to be used.</param>
            <returns>True if the given lines are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineToLine(Tekla.Structures.Geometry3d.Line,Tekla.Structures.Geometry3d.Line,System.Double)">
            <summary>
            Returns true if the given lines are parallel within the given angular tolerance.
            </summary>
            <param name="Line1">The first line to be used.</param>
            <param name="Line2">The second line to be used.</param>
            <param name="Tolerance">The angular tolerance (in radians) to be used.</param>
            <returns>True if the given lines are parallel within the given tolerance.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineSegmentToLineSegment(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.LineSegment)">
            <summary>
            Returns true if the given line segments are parallel.
            </summary>
            <param name="LineSegment1">The first line segment to be used.</param>
            <param name="LineSegment2">The second line segment to be used.</param>
            <returns>True if the given line segments are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineSegmentToLineSegment(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.LineSegment,System.Double)">
            <summary>
            Returns true if the given line segments are parallel within the given angular tolerance.
            </summary>
            <param name="LineSegment1">The first line segment to be used.</param>
            <param name="LineSegment2">The second line segment to be used.</param>
            <param name="Tolerance">The angular tolerance (in radians) to be used.</param>
            <returns>True if the given line segments are parallel within the given tolerance.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.PlaneToPlane(Tekla.Structures.Geometry3d.GeometricPlane,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns true if the given planes are parallel.
            </summary>
            <param name="Plane1">The first plane to be used.</param>
            <param name="Plane2">The second plane to be used.</param>
            <returns>True if the given planes are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.PlaneToPlane(Tekla.Structures.Geometry3d.GeometricPlane,Tekla.Structures.Geometry3d.GeometricPlane,System.Double)">
            <summary>
            Returns true if the given planes are parallel within the given angular tolerance.
            </summary>
            <param name="Plane1">The first plane to be used.</param>
            <param name="Plane2">The second plane to be used.</param>
            <param name="Tolerance">The angular tolerance (in radians) to be used.</param>
            <returns>True if the given planes are parallel within the given tolerance.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.VectorToPlane(Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns true if the given vector and plane are parallel.
            </summary>
            <param name="Vector">The vector to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>True if the given vector and plane are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.VectorToPlane(Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.GeometricPlane,System.Double)">
            <summary>
            Returns true if the given vector and plane are parallel within the given angular tolerance.
            </summary>
            <param name="Vector">The vector to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <param name="Tolerance">The angular tolerance (in radians) to be used.</param>
            <returns>True if the given vector and plane are parallel within the given tolerance.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineToPlane(Tekla.Structures.Geometry3d.Line,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns true if the given line and plane are parallel.
            </summary>
            <param name="Line">The line to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>True if the given line and plane are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineToPlane(Tekla.Structures.Geometry3d.Line,Tekla.Structures.Geometry3d.GeometricPlane,System.Double)">
            <summary>
            Returns true if the given line and plane are parallel within the given angular tolerance.
            </summary>
            <param name="Line">The line to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <param name="Tolerance">The angular tolerance (in radians) to be used.</param>
            <returns>True if the given line and plane are parallel within the given tolerance.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineSegmentToPlane(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns true if the given line segment and plane are parallel.
            </summary>
            <param name="LineSegment">The line segment to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>True if the given line segment and plane are parallel.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Parallel.LineSegmentToPlane(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.GeometricPlane,System.Double)">
            <summary>
            Returns true if the given line segment and plane are parallel within the given angular tolerance.
            </summary>
            <param name="LineSegment">The line segment to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <param name="Tolerance">The angular tolerance (in radians) to be used.</param>
            <returns>True if the given line segment and plane are parallel within the given tolerance.</returns>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Point">
            <summary>
            The Point class represents a single position in 3D space.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.Point.X">
            <summary>
            The X-coordinate of the point.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.Point.Y">
            <summary>
            The Y-coordinate of the point.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.Point.Z">
            <summary>
            The Z-coordinate of the point.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.Point.EPSILON_SQUARED">
            <summary>
            A boundary value for comparing if two points are similar.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Geometry3d.Point.HASH_SEED">
            <summary>
            A default seed value for creating hash codes.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.#ctor">
            <summary>
            Instantiates a point with zero members.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.#ctor(System.Double,System.Double,System.Double)">
            <summary>
            Instantiates a point with the given coordinates.
            </summary>
            <param name="X">The X-coordinate to be used.</param>
            <param name="Y">The Y-coordinate to be used.</param>
            <param name="Z">The Z-coordinate to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.#ctor(System.Double,System.Double)">
            <summary>
            Instantiates a point with the given coordinates and zero Z-coordinate.
            </summary>
            <param name="X">The X-coordinate to be used.</param>
            <param name="Y">The Y-coordinate to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.#ctor(Tekla.Structures.Geometry3d.Point)">
            <summary>
            Instantiates a point with the given other point.
            </summary>
            <param name="Point">The other point to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.Zero">
            <summary>
            Zeros all the members of the point.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.ToString">
            <summary>
            Formats the point into a string.
            </summary>
            <returns>The string that represents the point.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.Translate(System.Double,System.Double,System.Double)">
            <summary>
            Translates the point using the given vector.
            </summary>
            <param name="X">The X-translation to be used.</param>
            <param name="Y">The Y-translation to be used.</param>
            <param name="Z">The Z-translation to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.op_Addition(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Returns the sum of the given two points.
            </summary>
            <param name="p1">The first point to be used.</param>
            <param name="p2">The second point to be used.</param>
            <returns>The sum of the given two points.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.op_Subtraction(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Returns the difference of the given two points.
            </summary>
            <param name="p1">The first point to be used.</param>
            <param name="p2">The second point to be used.</param>
            <returns>The difference of the given two points.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.op_Equality(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Compares the given two points for similarity.
            </summary>
            <param name="p1">The first point to be used.</param>
            <param name="p2">The second point to be used.</param>
            <returns>True if the given two points are similar. False otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.op_Inequality(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Compares if the two given points are not similar.
            </summary>
            <param name="p1">The first point to be used.</param>
            <param name="p2">The second point to be used.</param>
            <returns>True if the given two points are not similar. False otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.Equals(System.Object)">
            <summary>
            Returns true if the current object and the given object are equal.
            </summary>
            <param name="obj">The object we wish to check the equality with.</param>
            <returns>True if the given object equals the current object.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.GetHashCode">
            <summary>
            Returns a hash code for the point.
            Notice, in extremely rare cases, you might not get the same
            hash code for two points even though they are considered equal! This 
            should, however, happen only in extremely rare cases!
            </summary>
            <returns>The hash code for the point.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Point.AreEqual(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Point)">
            <summary>
            Compares two points and tells whether they are equal.
            </summary>
            <param name="Point1">The first point to be compared.</param>
            <param name="Point2">The second point to be compared.</param>
            <returns>True if the points are equal, false otherwise.</returns>
        </member>
        <member name="T:Tekla.Structures.Internal.dotPoint_t">
            <summary>
            Struct for the Point.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotPoint_t.X">
            <summary>
            The points coordinates.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotPoint_t.Y">
            <summary>
            The points coordinates.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotPoint_t.Z">
            <summary>
            The points coordinates.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.dotPoint_t.ToStruct(Tekla.Structures.Geometry3d.Point)">
            <summary>
            Point to dotPoint_t.
            </summary>
            <param name="P"></param>
        </member>
        <member name="M:Tekla.Structures.Internal.dotPoint_t.FromStruct(Tekla.Structures.Geometry3d.Point)">
            <summary>
            dotPoint to Point.
            </summary>
            <param name="P"></param>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Projection">
            <summary>
            The Projection class contains methods for calculating the projection of geometric objects on
            other geometric objects.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Projection.PointToLine(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.Line)">
            <summary>
            Returns a new point which is a projection of the given point onto the given line.
            </summary>
            <param name="Point">The point to be used.</param>
            <param name="Line">The line to be used.</param>
            <returns>The new projection point.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Projection.PointToPlane(Tekla.Structures.Geometry3d.Point,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns a new point which is a projection of the given point onto the given plane.
            </summary>
            <param name="Point">The point to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>The new projection point.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Projection.LineToPlane(Tekla.Structures.Geometry3d.Line,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns a new line which is a projection of the given line onto the given plane.
            </summary>
            <param name="Line">The line to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>The new projection line.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Projection.LineSegmentToPlane(Tekla.Structures.Geometry3d.LineSegment,Tekla.Structures.Geometry3d.GeometricPlane)">
            <summary>
            Returns a new line segment which is a projection of the given line segment onto the given plane.
            </summary>
            <param name="LineSegment">The line segment to be used.</param>
            <param name="Plane">The plane to be used.</param>
            <returns>The new projection line segment.</returns>
        </member>
        <member name="T:Tekla.Structures.Identifier">
            <summary>
            The Identifier class represents an identifier that holds information
            about the identifier number of an object.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Identifier.ID2">
            <summary>
            Secondary identifier.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Identifier.#ctor">
            <summary>
            Constructs an empty indentifier.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Identifier.#ctor(System.Int32)">
            <summary>
            Constructs an identifier with the given ID.
            </summary>
            <param name="ID">The ID to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Identifier.#ctor(System.Guid)">
            <summary>
            Constructs an identifier with the given GUID.
            </summary>
            <param name="GUID">The GUID to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Identifier.#ctor(System.String)">
            <summary>
            Constructs an identifier with the given GUID string.
            </summary>
            <param name="GUID">The GUID to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Identifier.GetIdentifierByGUIDFromModel(System.String)">
            <summary>
            Returns an identifier instance that has the given GUID in the model.
            </summary>
            <param name="GUID">The GUID to look for in the model.</param>
            <returns>The identifier of the GUID, or null if not found.</returns>
        </member>
        <member name="M:Tekla.Structures.Identifier.ToString">
            <summary>
            Returns the integer ID as a string.
            </summary>
            <returns>The integer ID as a string.</returns>
        </member>
        <member name="M:Tekla.Structures.Identifier.Equals(Tekla.Structures.Identifier)">
            <summary>
            Compares the identifier with another identifier instance.
            </summary>
            <param name="OtherIdentifier">The identifier to compare with.</param>
            <returns>True if the identifiers are equal, false otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Identifier.Equals(System.Object)">
            <summary>
            Compares the identifier with another object instance.
            </summary>
            <param name="OtherObject">The object to compare with.</param>
            <returns>True if the objects are equal, false otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Identifier.GetHashCode">
            <summary>
            Gets the hash number of the identifier.
            </summary>
            <returns>The hashed number.</returns>
        </member>
        <member name="M:Tekla.Structures.Identifier.IsValid">
            <summary>
            Returns true if the identifier seems to be valid.
            The validation is done based on the ID or GUID property.
            </summary>
            <returns>True if the identifier seems to be valid.</returns>
        </member>
        <member name="M:Tekla.Structures.Identifier.SetGuid(System.String)">
            <summary>
            Sets GUID without resetting ID.
            </summary>
            <returns>True if the identifier seems to be valid.</returns>
        </member>
        <member name="P:Tekla.Structures.Identifier.ID">
            <summary>
            The identifier number.
            If the ID value is set manually, the GUID is initialized to Guid.Empty and the identification is done based on the ID.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Identifier.GUID">
            <summary>
            The object's globally unique identifier.
            If the GUID value is set manually, the ID is initialized to 0 and the identification is done based on the GUID.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Internal.dotIdentifier_t">
            <summary>
            Identifier class serialization.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotIdentifier_t.ID">
            <summary>
            ID.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotIdentifier_t.ID2">
            <summary>
            Sub ID.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotIdentifier_t.aGUID">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.dotIdentifier_t.ToStruct(Tekla.Structures.Identifier)">
            <summary>
            From Identifier to dotIdentifier_t.
            </summary>
            <param name="I"></param>
        </member>
        <member name="M:Tekla.Structures.Internal.dotIdentifier_t.FromStruct(Tekla.Structures.Identifier)">
            <summary>
            From dotIdentifier_t to Identifier.
            </summary>
            <param name="I"></param>
        </member>
        <member name="T:Tekla.Structures.Internal.dotIdentifierToGUID_t">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotIdentifierToGUID_t.Identifier">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotIdentifierToGUID_t.GUID">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.dotIdentifierToGUID_t.GetIdentifierByGUID(System.String)">
            <summary>
            Returns an identifier instance that has the given GUID in the model.
            </summary>
            <param name="GUID">The GUID to look for in the model.</param>
            <returns>The identifier of the GUID, with empty values if not found.</returns>
        </member>
        <member name="M:Tekla.Structures.Internal.dotIdentifierToGUID_t.GetGUIDByIdentifier(Tekla.Structures.Identifier)">
            <summary>
            Returns the GUID of the given identifier instance.
            </summary>
            <returns>The GUID of the identifier.</returns>
        </member>
        <member name="T:Tekla.Structures.Geometry3d.Vector">
            <summary>
            The Vector class defines a direction and magnitude from the current origin.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.#ctor">
            <summary>
            Instantiates a zero length vector.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.#ctor(System.Double,System.Double,System.Double)">
            <summary>
            Instantiates a vector with the given coordinates.
            </summary>
            <param name="X">The X-coordinate to be used.</param>
            <param name="Y">The Y-coordinate to be used.</param>
            <param name="Z">The Z-coordinate to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.#ctor(Tekla.Structures.Geometry3d.Point)">
            <summary>
            Instatiates a new vector with the given point.
            </summary>
            <param name="Point">The point to be used.</param>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.Normalize">
            <summary>
            Normalizes the vector using the length 1.0 (the length of a unit vector).
            </summary>
            <returns>The normalized vector's length.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.Normalize(System.Double)">
            <summary>
            Normalizes the vector using the given length.
            </summary>
            <param name="NewLength">The length to be used.</param>
            <returns>The normalized vector's length.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.GetLength">
            <summary>
            Gets the length (magnitude) of a vector.
            </summary>
            <returns>The vector's length.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.GetAngleBetween(Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Gets the angle (in radians) between the current vector and the given vector.
            </summary>
            <param name="Vector">The vector to be used.</param>
            <returns>The angle between the vectors in radians.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.GetNormal">
            <summary>
            Returns a new normalized equivalent of the current vector.
            </summary>
            <returns>The normalized equivalent of the current vector.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.Dot(Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Returns a dot product of the current vector and the given vector.
            </summary>
            <param name="Vector">The vector to be used.</param>
            <returns>The dot product of the vectors.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.Dot(Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Returns a dot product of the given two vectors.
            </summary>
            <param name="Vector1">The first vector to be used.</param>
            <param name="Vector2">The second vector to be used.</param>
            <returns>The dot product of the vectors.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.Cross(Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Returns a new cross product vector of the current vector and the given vector.
            </summary>
            <param name="Vector">The vector to be used.</param>
            <returns>The new cross product vector.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.Cross(Tekla.Structures.Geometry3d.Vector,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Returns a new cross product vector of the given two vectors.
            </summary>
            <param name="Vector1">The first vector to be used.</param>
            <param name="Vector2">The second vector to be used.</param>
            <returns>The new cross product vector.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.op_Multiply(Tekla.Structures.Geometry3d.Vector,System.Double)">
            <summary>
            Calculates the multiplication of the given vector with the given scalar.
            </summary>
            <param name="Vector">The vector to be multiplied.</param>
            <param name="Multiplier">The scalar to multiply the vector with.</param>
            <returns>The new vector that is the multiplication of the vector and the scalar.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.op_Multiply(System.Double,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Calculates the multiplication of the given vector with the given scalar.
            </summary>
            <param name="Multiplier">The scalar to multiply the vector with.</param>
            <param name="Vector">The vector to be multiplied.</param>
            <returns>The new vector that is the multiplication of the vector and the scalar.</returns>
        </member>
        <member name="M:Tekla.Structures.Geometry3d.Vector.ToString">
            <summary>
            Formats the 3D vector into a string with fixed decimals, in the following way: "(X, Y, Z)".
            </summary>
            <returns>The string that represents the vector.</returns>
        </member>
        <member name="T:Tekla.Structures.Internal.dotVector_t">
            <summary>
            Struct for Vector.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotVector_t.Direction">
            <summary>
            Vector data.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.dotVector_t.ToStruct(Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Vector to dotVector_t.
            </summary>
            <param name="vector"></param>
        </member>
        <member name="M:Tekla.Structures.Internal.dotVector_t.FromStruct(Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Vector from dotVector_t.
            </summary>
            <param name="vector"></param>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.AreWeUnitTesting`1">
            <summary>
            This is to enable unit-testing specific features like faking TS connection.
            This class hold a flag that is set to true when user wants to prevent 
            DelegateProxy's static constructor from running and creating IPC connection.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.AreWeUnitTesting`1.Value">
            <summary>
            Get or set whether we are running in unit testing
            environment where ICDelegate is replaced with a fake.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.ICDelegate">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.ICDelegate.ExportGetCurrentProgramVersion(Tekla.Structures.Internal.dotProgramVersion_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pProgramVersion"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.ICDelegate.ExportGetAdvancedOption(Tekla.Structures.Internal.dotGetAdvancedOption_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.ICDelegate.ExportGetSetClashCheckOptions(Tekla.Structures.Internal.dotClashCheckOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.ICDelegate.ExportGetSetComponentOptions(Tekla.Structures.Internal.dotComponentOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.ICDelegate.ExportGetModuleQuery(Tekla.Structures.Internal.dotModuleManagerQuery_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pModuleQuery"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.ICDelegate.ExportGetAnalysisModuleQuery(Tekla.Structures.Internal.DotAnalysisModuleManagerQuery@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pModuleQuery"></param>
            <returns>Returns the modules in use.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.ICDelegate.ExportGetIdentifierByGUID(Tekla.Structures.Internal.dotIdentifierToGUID_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pIdentifierToGUID"></param>
            <returns></returns>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.Instance">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.Functionality">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.#ctor(Tekla.Structures.TeklaStructuresInternal.ICDelegate,Tekla.Structures.Internal.WrapperFunctionalityBase)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="Instance"></param>
            <param name="Functionality"></param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.ExportGetCurrentProgramVersion(Tekla.Structures.Internal.dotProgramVersion_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pProgramVersion"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.ExportGetAdvancedOption(Tekla.Structures.Internal.dotGetAdvancedOption_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.ExportGetSetClashCheckOptions(Tekla.Structures.Internal.dotClashCheckOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.ExportGetSetComponentOptions(Tekla.Structures.Internal.dotComponentOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.ExportGetModuleQuery(Tekla.Structures.Internal.dotModuleManagerQuery_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pModuleQuery"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.ExportGetAnalysisModuleQuery(Tekla.Structures.Internal.DotAnalysisModuleManagerQuery@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pModuleQuery"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateWrapper.ExportGetIdentifierByGUID(Tekla.Structures.Internal.dotIdentifierToGUID_t@)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="pIdentifierToGUID"></param>
            <returns></returns>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.CDelegate.Singletons">
            <summary> Storage class for singleton instances. </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegate.Singletons.#cctor">
            <summary>
            Explicit static constructor to tell C# compiler
            not to mark type as beforefieldinit. Do not remove.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized">
            <summary>
            CDelegateSynchronized includes all the platform invokable commands and synchronization to AKIT.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.#ctor">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.BeginInvoke(System.Delegate,System.Object[])">
            <summary> Executes the delegate on the main thread that this object executes on. </summary>
            <param name="Method">The given method.</param>
            <param name="Args">An array of type <see cref="T:System.Object"/> to pass as arguments to the given method. This can be null if no arguments are needed. </param>
            <returns> An <see cref="T:System.IAsyncResult"/> interface that represents the asynchronous operation started by calling this method. </returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.EndInvoke(System.IAsyncResult)">
            <summary> Waits until the process started by calling <see cref="M:System.ComponentModel.ISynchronizeInvoke.BeginInvoke(System.Delegate,System.Object[])"/> completes, and then returns the value generated by the process. </summary>
            <param name="Result">An <see cref="T:System.IAsyncResult"/> interface that represents the asynchronous operation started by calling <see cref="M:System.ComponentModel.ISynchronizeInvoke.BeginInvoke(System.Delegate,System.Object[])"/>. </param>
            <returns> An <see cref="T:System.Object"/> that represents the return value generated by the asynchronous operation. </returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.Invoke(System.Delegate,System.Object[])">
            <summary> Executes the delegate on the main thread that this object executes on. </summary>
            <param name="Method">The given method.</param>
            <param name="Args">An array of type <see cref="T:System.Object"/> to pass as arguments to the given method. This can be null if no arguments are needed. </param>
            <returns> An <see cref="T:System.Object"/> that represents the return value from the delegate being invoked, or null if the delegate has no return value. </returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.ExportGetCurrentProgramVersion(Tekla.Structures.Internal.dotProgramVersion_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pProgramVersion"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.ExportGetAdvancedOption(Tekla.Structures.Internal.dotGetAdvancedOption_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.ExportGetSetClashCheckOptions(Tekla.Structures.Internal.dotClashCheckOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.ExportGetSetComponentOptions(Tekla.Structures.Internal.dotComponentOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.ExportGetModuleQuery(Tekla.Structures.Internal.dotModuleManagerQuery_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pModuleQuery"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.ExportGetAnalysisModuleQuery(Tekla.Structures.Internal.DotAnalysisModuleManagerQuery@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pModuleQuery">The module query.</param>
            <returns>Returns TRUE if successfully retrieved modules in use.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.ExportGetIdentifierByGUID(Tekla.Structures.Internal.dotIdentifierToGUID_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pIdentifierToGUID">The GUID query.</param>
            <returns>Returns TRUE if successfull.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.dotExportGetCurrentProgramVersion(Tekla.Structures.Internal.dotProgramVersion_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pProgramVersion"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.dotExportGetAdvancedOption(Tekla.Structures.Internal.dotGetAdvancedOption_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.dotExportGetSetClashCheckOptions(Tekla.Structures.Internal.dotClashCheckOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.dotExportGetSetComponentOptions(Tekla.Structures.Internal.dotComponentOptions_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pArgument"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.dotExportGetModuleQuery(Tekla.Structures.Internal.dotModuleManagerQuery_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pModuleQuery"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.DotAnalysisExportGetAnalysisModuleQuery(Tekla.Structures.Internal.DotAnalysisModuleManagerQuery@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pModuleQuery">The module query.</param>
            <returns>Returns TRUE on success.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.dotExportGetIdentifierByGUID(Tekla.Structures.Internal.dotIdentifierToGUID_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="pIdentifierToGUID">The identifier.</param>
            <returns>Returns TRUE on success.</returns>
        </member>
        <member name="P:Tekla.Structures.TeklaStructuresInternal.CDelegateSynchronized.InvokeRequired">
            <summary> Gets a value indicating whether the caller must call <see cref="M:System.ComponentModel.ISynchronizeInvoke.Invoke(System.Delegate,System.Object[])"/> when calling an object that implements this interface. </summary>
            <returns> true if the caller must call <see cref="M:System.ComponentModel.ISynchronizeInvoke.Invoke(System.Delegate,System.Object[])"/>; otherwise, false. </returns>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.DelegateProxy">
            <summary>
            CDelegate remote class proxy.
            </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.DelegateProxy._Instance">
            <summary>
            The remote delegate object.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.DelegateProxy.#cctor">
            <summary>
            Initializes static instance variable.
            </summary>
        </member>
        <member name="P:Tekla.Structures.TeklaStructuresInternal.DelegateProxy.Delegate">
            <summary>
            Gets the singleton CDelegate instance that includes model API methods.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.DotNetProxy">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.DotNetProxy.Run(System.String)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="Param">Not used.</param>
            <returns>The return value is not usefull because AKIT doesn't send it to Core.</returns>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.EventHandler">
            <summary>
            EventHandler is a class that TS calls when an event is raised. Handler
            then calls all the listener "Events" objects.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.IEventHandler">
            <summary>
            IEventHandler is an interface that TS calls when an event is raised.
            Handler implementation then calls all the listener "Events" objects.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.IEventHandler.AddListener(Tekla.Structures.TeklaStructuresInternal.Events)">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.IEventHandler.RemoveListener(Tekla.Structures.TeklaStructuresInternal.Events)">
            <exclude/>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.EventHandler.EventListeners">
            <summary> List of Events objects waiting to be called </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.EventHandler.AddListener(Tekla.Structures.TeklaStructuresInternal.Events)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="EventListener"></param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.EventHandler.RemoveListener(Tekla.Structures.TeklaStructuresInternal.Events)">
            <summary> DO NOT USE! For internal usage only! </summary>
            <param name="EventListener"></param>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.EventHandler.OnEvent(System.String)">
            <summary> TS calls when event is raised. </summary>
            <param name="Param">Name of the event</param>
            <returns>1</returns>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.EventHandlerProxy">
            <summary> EventHandler remote class proxy. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.EventHandlerProxy.Instance">
            <summary> The remote EventHandler object. </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.EventHandlerProxy.#cctor">
            <summary> Initializes static instance variable. </summary>
        </member>
        <member name="P:Tekla.Structures.TeklaStructuresInternal.EventHandlerProxy.EventHandler">
            <summary> Gets the singleton EventHandler instance that includes model API event handlers. </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.EventHandlerWrapper">
            <exclude/>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.EventHandlerWrapper.Instance">
            <exclude/>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.EventHandlerWrapper.Functionality">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.EventHandlerWrapper.#ctor(Tekla.Structures.TeklaStructuresInternal.IEventHandler,Tekla.Structures.Internal.WrapperFunctionalityBase)">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.EventHandlerWrapper.AddListener(Tekla.Structures.TeklaStructuresInternal.Events)">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.EventHandlerWrapper.RemoveListener(Tekla.Structures.TeklaStructuresInternal.Events)">
            <exclude/>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInternal.Remoter">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresInternal.Remoter.ChannelName">
            <summary> Name of the remoting channel to register. </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Remoter.#ctor">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Remoter.PublishTypes">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInternal.Remoter.InitializeSandBox">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="T:Tekla.Structures.Solid.Face">
            <summary>
            The Face class represents a single face in a solid.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Solid.Face.#ctor(System.Collections.ArrayList,Tekla.Structures.Geometry3d.Vector)">
            <summary>
            Instantiates a new face.
            </summary>
            <param name="LoopList">A list of loops in the new face.</param>
            <param name="NormalVector">A normal vector of the new face.</param>
        </member>
        <member name="M:Tekla.Structures.Solid.Face.GetLoopEnumerator">
            <summary>
            Gets a new loop enumerator for enumerating through the face's loops.
            </summary>
            <returns>The new loop enumerator.</returns>
        </member>
        <member name="P:Tekla.Structures.Solid.Face.Normal">
            <summary>
            The normal vector of the face.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Solid.FaceEnumerator">
            <summary>
            The FaceEnumerator class is used to enumerate the faces of a solid.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Solid.FaceEnumerator.#ctor(System.Collections.IEnumerator,System.Collections.IEnumerator)">
            <summary>
            Instantiates a new face enumerator. This constructor should
            only be called by someone inheriting from ISolid.
            </summary>
            <param name="Enumerator">The father solid's face list enumerator.</param>
            <param name="VectorEnumerator">The father solid's face normal vector enumerator.</param>
        </member>
        <member name="M:Tekla.Structures.Solid.FaceEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Solid.FaceEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Solid.FaceEnumerator.Current">
            <summary>
            Returns the current face.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Solid.ISolid">
            <summary>
            The ISolid interface represents a physical object in the model or a drawing created by a part instance.
            The solid instance can be used to query the actual geometry of the part.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Solid.ISolid.GetFaceEnumerator">
            <summary>
            Returns a new face enumerator in the current plane.
            </summary>
            <returns>A face enumerator to enumerate through the solid's faces.</returns>
        </member>
        <member name="P:Tekla.Structures.Solid.ISolid.MinimumPoint">
            <summary>
            The minimum axis-aligned point of the solid in the current plane.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Solid.ISolid.MaximumPoint">
            <summary>
            The maximum axis-aligned point of the solid in the current plane.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Solid.Loop">
            <summary>
            The Loop class represents a single loop in a face.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Solid.Loop.#ctor(System.Collections.ArrayList)">
            <summary>
            Instantiates a new loop.
            </summary>
            <param name="VertexList">A list of vertexes that will be in the new loop.</param>
        </member>
        <member name="M:Tekla.Structures.Solid.Loop.GetVertexEnumerator">
            <summary>
            Gets a new vertex enumerator.
            </summary>
            <returns>A vertex enumerator to enumerate through the vertexes in the loop.</returns>
        </member>
        <member name="T:Tekla.Structures.Solid.LoopEnumerator">
            <summary>
            The LoopEnumerator class is used to enumerate the loops of a face instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Solid.LoopEnumerator.#ctor(System.Collections.IEnumerator)">
            <summary>
            Instantiates a new loop enumerator.
            </summary>
            <param name="Enumerator">The father face's loop list enumerator.</param>
        </member>
        <member name="M:Tekla.Structures.Solid.LoopEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Solid.LoopEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Solid.LoopEnumerator.Current">
            <summary>
            Returns the current loop.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Solid.VertexEnumerator">
            <summary>
            The VertexEnumerator class is used to enumerate the vertexes of a loop instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Solid.VertexEnumerator.#ctor(System.Collections.IEnumerator)">
            <summary>
            Instantiates a new vertex enumerator.
            </summary>
            <param name="Enumerator">The father loop's vertex list enumerator.</param>
        </member>
        <member name="M:Tekla.Structures.Solid.VertexEnumerator.MoveNext">
            <summary>
            Moves to the next item in the enumerator. 
            </summary>
            <returns>False on failure.</returns>
        </member>
        <member name="M:Tekla.Structures.Solid.VertexEnumerator.Reset">
            <summary>
            Resets the enumerator to the beginning.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Solid.VertexEnumerator.Current">
            <summary>
            Returns the current vertex.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresDatabaseTypeEnum">
            <summary> The object types to be used in filter expressions.</summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.UNKNOWN">
            <summary> The unknown type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.POINT">
            <summary> The point type, this is not a ModelObject. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.PART">
            <summary> The part type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.CONNECTION">
            <summary> The connection type, used for details and seams as well. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.COMPONENT">
            <summary> The component type, used for macros, plug-ins and custom parts. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.GRID">
            <summary> The grid type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.FITTING">
            <summary> The fitting type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.BOLT">
            <summary> The bolt type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.PART_CUT">
            <summary> The part cut type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.PLANE_CUT">
            <summary> The plane cut type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.WELDING">
            <summary> The welding type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.ASSEMBLY">
            <summary> The assembly type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.CONSTRUCTION_LINE">
            <summary> The construction line type, this is not a ModelObject. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.PLANE">
            <summary> The plane type, used for control planes and grid planes.</summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.PART_ADD">
            <summary> The part add type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.CONSTRUCTION_CIRCLE">
            <summary> The construction circle type, this is not a ModelObject. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.REBAR">
            <summary> The rebar type, used for single rebars, groups, meshes and strands.</summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.LOAD_POINT">
            <summary> The load point type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.LOAD_LINE">
            <summary> The load line type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.LOAD_AREA">
            <summary> The load area type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.LOAD_UNIFORM">
            <summary> The load uniform type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.LOAD_GROUP">
            <summary> The load group type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.SURFACE_TREATMENT">
            <summary> The surface treatment type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.REBAR_SPLICE">
            <summary> The rebar splice type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.EDGE_CHAMFER">
            <summary> The edge chamfer type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.TASK">
            <summary> The task type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.TASK_DEPENDENCY">
            <summary> The task dependency type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.TASK_WORKTYPE">
            <summary> The task worktype type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.HIERARCHIC_DEFINITION">
            <summary> The hierarchic definition type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.HIERARCHIC_OBJECT">
            <summary> The hierarchic object type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.DB_POUR_BREAK">
            <summary> The pour break type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.DB_POUR_OBJECT">
            <summary> The pour object type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.REFERENCE_MODEL">
            <summary> The reference model type. </summary>
        </member>
        <member name="F:Tekla.Structures.TeklaStructuresDatabaseTypeEnum.REFERENCE_MODEL_OBJECT">
            <summary> The reference model object type. </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresInfo">
            <summary>The TeklaStructuresInfo class provides information about Tekla Structures.</summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresInfo.GetCurrentProgramVersion">
            <summary> Returns the current Tekla Structures version. </summary>
            <returns>The current version of Tekla Structures.</returns>
        </member>
        <member name="T:Tekla.Structures.Internal.dotProgramVersion_t">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotProgramVersion_t.aProgramVersion">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="T:Tekla.Structures.ClashCheckOptions">
            <summary>The ClashCheckOptions class represents the clash check options.</summary>
        </member>
        <member name="P:Tekla.Structures.ClashCheckOptions.BoltHeadDiameter">
            <summary>The bolt head diameter.</summary>
        </member>
        <member name="P:Tekla.Structures.ClashCheckOptions.NutThickness">
            <summary>The nut thickness.</summary>
        </member>
        <member name="T:Tekla.Structures.ComponentOptions">
            <summary>The ComponentOptions class represents the component options.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.PlateProfileName">
            <summary>The plate profile name.</summary>
             <remarks>The maximum length is 10 characters.</remarks>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.FoldedPlateProfileName">
            <summary>The folded plate profile name.</summary>
             <remarks>The maximum length is 10 characters.</remarks>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.BoltEdgeDistanceFactor">
            <summary>The factor of bolt edge distance.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.BoltEdgeDistanceReference">
            <summary>The bolt edge distance reference.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.BoltStandard">
            <summary> The bolt standard.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.BoltSize">
            <summary>The bolt size.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.PartMaterial">
            <summary>The part material.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.PartWeldedToPrimaryStartNumber">
            <summary>The part welded to primary start number.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.PartWeldedToPrimaryPositionPrefix">
            <summary>The part welded to primary position prefix.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.PartWeldedToSecondaryStartNumber">
            <summary>The part welded to secondary start number.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.PartWeldedToSecondaryPositionPrefix">
            <summary>The part welded to secondary position prefix.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.LoosePartStartNumber">
            <summary>The loose part start number.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.LoosePartPositionPrefix">
            <summary>The loose part position prefix.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.AssemblyLoosePartStartNumber">
            <summary>The assembly loose part number.</summary>
        </member>
        <member name="P:Tekla.Structures.ComponentOptions.AssemblyLoosePartPositionPrefix">
            <summary>The assembly loose position prefix.</summary>
        </member>
        <member name="T:Tekla.Structures.ComponentOptions.BoltEdgeDistanceReferenceEnum">
            <summary> The bolt edge distance reference type. </summary>
        </member>
        <member name="F:Tekla.Structures.ComponentOptions.BoltEdgeDistanceReferenceEnum.BOLT_DIAMETER">
            <summary> Compares the edge distance to the bolt diameter. </summary>
        </member>
        <member name="F:Tekla.Structures.ComponentOptions.BoltEdgeDistanceReferenceEnum.HOLE_DIAMETER">
            <summary> Compares the edge distance to the hole diameter. </summary>
        </member>
        <member name="T:Tekla.Structures.DrawingDimensionsOptions">
            <summary>The DrawingDimensionsOptions class represents the drawing dimensions options.</summary>
        </member>
        <member name="T:Tekla.Structures.DrawingObjectsOptions">
            <summary>The DrawingObjectsOptions class represents the drawing objects options.</summary>
        </member>
        <member name="T:Tekla.Structures.GeneralOptions">
            <summary>The GeneralOptions class represents the general options.</summary>
        </member>
        <member name="T:Tekla.Structures.LoadModelingOptions">
            <summary>The LoadModelingOptions class represents the load modeling options.</summary>
        </member>
        <member name="T:Tekla.Structures.MouseSettingsOptions">
            <summary>The MouseSettingsOptions class represents the mouse settings options.</summary>
        </member>
        <member name="T:Tekla.Structures.NumberingOptions">
            <summary>The NumberingOptions class represents the numbering options.</summary>
        </member>
        <member name="T:Tekla.Structures.OrientationMarksOptions">
            <summary>The OrientationMarksOptions class represents the orientation marks options.</summary>
        </member>
        <member name="T:Tekla.Structures.UnitsAndDecimalsOptions">
            <summary>The UnitsAndDecimalsOptions class represents the units and decimals options.</summary>
        </member>
        <member name="T:Tekla.Structures.Internal.dotClashCheckOptions_t">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotClashCheckOptions_t.BoltHeadDiameter">
            <summary> Bolt Head Diameter.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotClashCheckOptions_t.NutThickness">
            <summary> Nut Thickness.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotClashCheckOptions_t.GetSet">
            <summary> Get = 0; Set = 1;</summary>
        </member>
        <member name="T:Tekla.Structures.Internal.dotComponentOptions_t">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.PlateProfileName">
            <summary>The plate profile name.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.FoldedPlateProfileName">
            <summary>The folded plate profile name.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.BoltEdgeDistanceFactor">
            <summary>The factor of bolt edge distance.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.BoltEdgeDistanceReference">
            <summary> The bolt edge distance reference.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.BoltStandard">
            <summary> The bolt standard.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.BoltSize">
            <summary>The bolt size.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.PartMaterial">
            <summary>The part material.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.PartWeldedToPrimaryStartNumber">
            <summary>The part welded to primary start number.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.PartWeldedToSecondaryStartNumber">
            <summary>The part welded to secondary start number.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.LoosePartStartNumber">
            <summary>The loose part start number.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.AssemblyLoosePartStartNumber">
            <summary>The assembly loose part number.</summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotComponentOptions_t.GetSet">
            <summary> Get = 0; Set = 1;</summary>
        </member>
        <member name="T:Tekla.Structures.PropertyTypeEnum">
            <summary> The type of property: int/double/string.</summary>
        </member>
        <member name="F:Tekla.Structures.PropertyTypeEnum.TYPE_INT">
            <summary>
            The integer property type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.PropertyTypeEnum.TYPE_DOUBLE">
            <summary>
            The double property type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.PropertyTypeEnum.TYPE_STRING">
            <summary>
            The string property type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.TeklaStructuresSettings">
            <summary>
            The TeklaStructuresSettings class provides methods to inquire application settings.
            </summary>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.GetAdvancedOption(System.String,System.Boolean@)">
            <summary>
            Returns the value of an advanced option variable.
            </summary>
            <param name="VariableName">The name of the advanced option.</param>
            <param name="Value">The returned value of the advanced option.</param>
            <returns>True if the value was successfully retrieved.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.GetAdvancedOption(System.String,System.Int32@)">
            <summary>
            Returns the value of an advanced option variable.
            </summary>
            <param name="VariableName">The name of the advanced option.</param>
            <param name="Value">The returned value of the advanced option.</param>
            <returns>True if the value was successfully retrieved.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.GetAdvancedOption(System.String,System.String@)">
            <summary>
            Returns the value of an advanced option variable.
            </summary>
            <param name="VariableName">The name of the advanced option.</param>
            <param name="Value">The returned value of the advanced option.</param>
            <returns>True if the value was successfully retrieved.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.GetAdvancedOption(System.String,System.Double@)">
            <summary>
            Returns the value of an advanced option variable.
            </summary>
            <param name="VariableName">The name of the advanced option.</param>
            <param name="Value">The returned value of the advanced option.</param>
            <returns>True if the value was successfully retrieved.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.GetOptions(Tekla.Structures.ClashCheckOptions@)">
            <summary>Returns the value of the clash check options.</summary>
            <param name="Options">The returned value of the clash check options.</param>
            <returns>True if the values were successfully retrieved.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.SetOptions(Tekla.Structures.ClashCheckOptions)">
            <summary>Sets the clash check options.</summary>
            <param name="Options">The clash check options to be set.</param>
            <returns>True if the clash check options were successfully set.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.GetOptions(Tekla.Structures.ComponentOptions@)">
            <summary>Returns the value of the component options.</summary>
            <param name="Options">The returned value of the component options.</param>
            <returns>True if the values were successfully retrieved.</returns>
        </member>
        <member name="M:Tekla.Structures.TeklaStructuresSettings.SetOptions(Tekla.Structures.ComponentOptions)">
            <summary>Sets the component options.</summary>
            <param name="Options">The component options to be set.</param>
            <returns>True if the component options were successfully set.</returns>
        </member>
        <member name="T:Tekla.Structures.Internal.dotGetAdvancedOption_t">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="M:Tekla.Structures.Internal.dotGetAdvancedOption_t.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
            <param name="OptionName"></param>
            <param name="OptionType"></param>
            <param name="ValueStringIteration"></param>
        </member>
        <member name="F:Tekla.Structures.Internal.dotGetAdvancedOption_t.aName">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotGetAdvancedOption_t.Type">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotGetAdvancedOption_t.ValueBool">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotGetAdvancedOption_t.ValueInt">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotGetAdvancedOption_t.aValueString">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotGetAdvancedOption_t.ValueDouble">
            <summary>
            DO NOT USE! For internal usage only!
            </summary>
        </member>
        <member name="F:Tekla.Structures.Internal.dotGetAdvancedOption_t.ValueStringIteration">
            <summary>
            From API to core tells chunk to receive, from core to API tells if there are more chunks to be fetched.
            </summary>
        </member>
    </members>
</doc>
