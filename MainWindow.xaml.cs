﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using TeklaTool.Models;
using TeklaTool.ViewModels;

namespace TeklaTool
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private MainViewModel _viewModel;

        public MainWindow()
        {
            InitializeComponent();
            _viewModel = (MainViewModel)DataContext;

            // 初始化零件数据网格列
            InitializePartsDataGrid();

            // 初始化构件数据网格列
            InitializeAssembliesDataGrid();

            // 注册窗口加载事件
            Loaded += MainWindow_Loaded;
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("========== MainWindow_Loaded 开始 ==========");

                // 窗口加载完成后的初始化操作
                Title = $"TeklaList - {System.Reflection.Assembly.GetExecutingAssembly().GetName().Version}";
                System.Diagnostics.Debug.WriteLine($"设置窗口标题: {Title}");

                // 设置默认排序
                SetDefaultSorting();
                System.Diagnostics.Debug.WriteLine("已设置默认排序");

                // 监听视图模式变更，以便在切换模式时应用正确的排序
                _viewModel.PropertyChanged += ViewModel_PropertyChanged;
                System.Diagnostics.Debug.WriteLine("已添加视图模式变更事件处理");

                // 不再需要设置测试数据
                System.Diagnostics.Debug.WriteLine("使用新的筛选控件，不需要设置测试数据");

                System.Diagnostics.Debug.WriteLine("========== MainWindow_Loaded 结束 ==========");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"窗口加载时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
            }
        }

        // 清除所有筛选按钮点击事件
        private void ClearAllFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[MainWindow] 清除所有筛选按钮点击");

                // 根据当前显示的数据网格，调用相应的清除筛选方法
                if (_viewModel.IsAssemblyMode)
                {
                    // 构件模式
                    var assembliesGrid = (Views.FilterableDataGrid)AssembliesDataGrid2;
                    assembliesGrid.ClearFilters();
                }
                else
                {
                    // 零件模式
                    var partsGrid = (Views.FilterableDataGrid)PartsDataGrid2;
                    partsGrid.ClearFilters();
                }

                System.Diagnostics.Debug.WriteLine("[MainWindow] 清除所有筛选完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MainWindow] 清除所有筛选时出错: {ex.Message}");
                MessageBox.Show($"清除筛选时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializePartsDataGrid()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("========== 初始化零件数据网格 ==========");

                // 添加零件数据网格的列
                var partsGrid = (Views.FilterableDataGrid)PartsDataGrid2;
                System.Diagnostics.Debug.WriteLine("获取到零件数据网格控件");

                // 添加列
                System.Diagnostics.Debug.WriteLine("开始添加列...");
                partsGrid.AddColumn("序号", "Index");
                partsGrid.AddColumn("构件编号", "AssemblyNumber");
                partsGrid.AddColumn("零件编号", "PartNumber");
                partsGrid.AddColumn("数量", "Count");
                partsGrid.AddColumn("名称", "Name");
                partsGrid.AddColumn("截面", "Profile");
                partsGrid.AddColumn("材质", "Material");
                partsGrid.AddColumn("表面处理", "Finish");
                partsGrid.AddColumn("等级", "Class");
                partsGrid.AddColumn("阶段", "Phase");
                partsGrid.AddColumn("螺栓数", "BoltCount");
                partsGrid.AddColumn("主/次", "MainPartDisplay");
                partsGrid.AddColumn("构件前缀", "AssemblyPrefix");
                partsGrid.AddColumn("构件起始号", "AssemblyStartNumber");
                partsGrid.AddColumn("零件前缀", "PartPrefix");
                partsGrid.AddColumn("零件起始号", "PartStartNumber");
                partsGrid.AddColumn("GUID", "Guid");
                partsGrid.AddColumn("备注", "Remark");
                System.Diagnostics.Debug.WriteLine($"已添加 {partsGrid.MainDataGrid.Columns.Count} 列");

                // 添加选择变更事件处理
                partsGrid.MainDataGrid.SelectionChanged += PartsDataGrid_SelectionChanged;
                System.Diagnostics.Debug.WriteLine("已添加选择变更事件处理");

                System.Diagnostics.Debug.WriteLine("========== 零件数据网格初始化完成 ==========");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化零件数据网格时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                MessageBox.Show($"初始化零件数据网格时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeAssembliesDataGrid()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("========== 初始化构件数据网格 ==========");

                // 添加构件数据网格的列
                var assembliesGrid = (Views.FilterableDataGrid)AssembliesDataGrid2;
                System.Diagnostics.Debug.WriteLine("获取到构件数据网格控件");

                // 添加列
                System.Diagnostics.Debug.WriteLine("开始添加列...");
                assembliesGrid.AddColumn("序号", "Index");
                assembliesGrid.AddColumn("构件编号", "AssemblyNumber");
                assembliesGrid.AddColumn("数量", "Count");
                assembliesGrid.AddColumn("名称", "Name");
                assembliesGrid.AddColumn("截面", "Profile");
                assembliesGrid.AddColumn("材质", "Material");
                assembliesGrid.AddColumn("表面处理", "Finish");
                assembliesGrid.AddColumn("等级", "Class");
                assembliesGrid.AddColumn("阶段", "Phase");
                assembliesGrid.AddColumn("零件数", "PartCount");
                assembliesGrid.AddColumn("构件前缀", "AssemblyPrefix");
                assembliesGrid.AddColumn("构件起始号", "AssemblyStartNumber");
                assembliesGrid.AddColumn("GUID", "Guid");
                assembliesGrid.AddColumn("备注", "Remark");
                System.Diagnostics.Debug.WriteLine($"已添加 {assembliesGrid.MainDataGrid.Columns.Count} 列");

                // 添加选择变更事件处理
                assembliesGrid.MainDataGrid.SelectionChanged += AssembliesDataGrid_SelectionChanged;
                System.Diagnostics.Debug.WriteLine("已添加选择变更事件处理");

                System.Diagnostics.Debug.WriteLine("========== 构件数据网格初始化完成 ==========");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化构件数据网格时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                MessageBox.Show($"初始化构件数据网格时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            // 当视图模式变更时，重新应用默认排序
            if (e.PropertyName == nameof(_viewModel.IsAssemblyMode))
            {
                SetDefaultSorting();
            }
        }

        /// <summary>
        /// 设置默认排序
        /// </summary>
        private void SetDefaultSorting()
        {
            try
            {
                // 根据当前模式设置默认排序
                if (_viewModel.IsAssemblyMode)
                {
                    // 构件模式：按构件编号排序
                    var assembliesGrid = (Views.FilterableDataGrid)AssembliesDataGrid2;
                    assembliesGrid.SetDefaultSort("AssemblyNumber", ListSortDirection.Ascending);
                }
                else
                {
                    // 零件模式：按零件编号排序
                    var partsGrid = (Views.FilterableDataGrid)PartsDataGrid2;
                    partsGrid.SetDefaultSort("PartNumber", ListSortDirection.Ascending);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置默认排序时出错: {ex.Message}");
            }
        }

        // 防抖动计时器
        private System.Timers.Timer _partsSelectionDebounceTimer;
        private System.Timers.Timer _assembliesSelectionDebounceTimer;
        private object _partsSelectionLock = new object();
        private object _assembliesSelectionLock = new object();

        private void PartsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is DataGrid dataGrid)
            {
                lock (_partsSelectionLock)
                {
                    // 如果计时器已存在，重置它
                    if (_partsSelectionDebounceTimer != null)
                    {
                        _partsSelectionDebounceTimer.Stop();
                        _partsSelectionDebounceTimer.Dispose();
                    }

                    // 创建新的计时器，延迟300毫秒执行
                    _partsSelectionDebounceTimer = new System.Timers.Timer(300);
                    _partsSelectionDebounceTimer.AutoReset = false;
                    _partsSelectionDebounceTimer.Elapsed += (s, args) =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                if (_viewModel.IsMergeRows)
                                {
                                    try
                                    {
                                        // 合并模式下，选中MergedPartRow，需找到所有对应TeklaModelPart
                                        var selectedItems = new List<TeklaModelPart>();

                                        // 检查每个选中项的类型
                                        foreach (var item in dataGrid.SelectedItems)
                                        {
                                            if (item is TeklaTool.ViewModels.PartListViewModel.MergedPartRow mergedRow)
                                            {
                                                // 找到所有具有相同零件编号的零件
                                                var matchingParts = _viewModel.Parts.Where(p => p.PartNumber == mergedRow.PartNumber).ToList();
                                                selectedItems.AddRange(matchingParts);
                                            }
                                            else if (item is TeklaModelPart part)
                                            {
                                                // 如果直接是零件，也添加到列表中
                                                selectedItems.Add(part);
                                            }
                                        }

                                        if (selectedItems.Count > 0)
                                        {
                                            _viewModel.PartList.HandlePartSelectionChanged(selectedItems);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理合并行选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                                else
                                {
                                    try
                                    {
                                        var selectedItems = dataGrid.SelectedItems.Cast<TeklaModelPart>().ToList();
                                        _viewModel.PartList.HandlePartSelectionChanged(selectedItems);
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"处理选择变更时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        });
                    };

                    _partsSelectionDebounceTimer.Start();
                }
            }
        }

        private void AssembliesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is DataGrid dataGrid)
            {
                lock (_assembliesSelectionLock)
                {
                    // 如果计时器已存在，重置它
                    if (_assembliesSelectionDebounceTimer != null)
                    {
                        _assembliesSelectionDebounceTimer.Stop();
                        _assembliesSelectionDebounceTimer.Dispose();
                    }

                    // 创建新的计时器，延迟300毫秒执行
                    _assembliesSelectionDebounceTimer = new System.Timers.Timer(300);
                    _assembliesSelectionDebounceTimer.AutoReset = false;
                    _assembliesSelectionDebounceTimer.Elapsed += (s, args) =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                if (_viewModel.IsMergeRows)
                                {
                                    try
                                    {
                                        // 合并模式下，选中MergedAssemblyRow，需找到所有对应AssemblyInfo
                                        var selectedItems = new List<AssemblyInfo>();

                                        // 检查每个选中项的类型
                                        foreach (var item in dataGrid.SelectedItems)
                                        {
                                            if (item is TeklaTool.ViewModels.AssemblyListViewModel.MergedAssemblyRow mergedRow)
                                            {
                                                // 找到所有具有相同构件编号的构件
                                                var matchingAssemblies = _viewModel.Assemblies.Where(a => a.AssemblyNumber == mergedRow.AssemblyNumber).ToList();
                                                
                                                // 确保至少有一个构件被添加到选中项中
                                                if (matchingAssemblies.Count == 0)
                                                {
                                                    // 如果没有找到匹配的构件，创建一个临时构件对象用于高亮
                                                    var tempAssembly = new AssemblyInfo
                                                    {
                                                        AssemblyNumber = mergedRow.AssemblyNumber,
                                                        ModelObjectId = mergedRow.ModelObjectIds?.FirstOrDefault() ?? 0,
                                                        Name = mergedRow.Name,
                                                        Profile = mergedRow.Profile,
                                                        Material = mergedRow.Material,
                                                        Finish = mergedRow.Finish,
                                                        Class = mergedRow.Class,
                                                        Phase = mergedRow.Phase,
                                                        AssemblyPrefix = mergedRow.AssemblyPrefix,
                                                        AssemblyStartNumber = mergedRow.AssemblyStartNumber,
                                                        Guid = mergedRow.Guid,
                                                        Remark = mergedRow.Remark
                                                    };
                                                    selectedItems.Add(tempAssembly);
                                                }
                                                else
                                                {
                                                    selectedItems.AddRange(matchingAssemblies);
                                                }
                                            }
                                            else if (item is AssemblyInfo assembly)
                                            {
                                                // 如果直接是构件，也添加到列表中
                                                selectedItems.Add(assembly);
                                            }
                                        }

                                        if (selectedItems.Count > 0)
                                        {
                                            _viewModel.AssemblyList.HandleAssemblySelectionChanged(selectedItems);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理合并行选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                                else
                                {
                                    try
                                    {
                                        var selectedItems = dataGrid.SelectedItems.Cast<AssemblyInfo>().ToList();
                                        _viewModel.AssemblyList.HandleAssemblySelectionChanged(selectedItems);
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"处理选择变更时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        });
                    };

                    _assembliesSelectionDebounceTimer.Start();
                }
            }
        }
    }
}