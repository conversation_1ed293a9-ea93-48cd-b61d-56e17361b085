<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Tekla.Structures.Dialog</name>
    </assembly>
    <members>
        <member name="T:Tekla.Structures.Dialog.ErrorDialog">
            <summary>
            The ErrorDialog class represents the common error dialog for the Open API.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.ErrorDialog.#ctor(System.String,System.Collections.Generic.List{System.String},Tekla.Structures.Dialog.ErrorDialog.Severity)">
            <summary>
            Initializes the common error message dialog.
            </summary>
            <param name="Message">The message to display in the dialog.</param>
            <param name="Details">The detail text to display in the message dialog.</param>
            <param name="Severity">The severity of the message.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.ErrorDialog.Show(System.String,System.String,Tekla.Structures.Dialog.ErrorDialog.Severity)">
            <summary>
            Shows a new error dialog.
            </summary>
            <param name="Message">The message to display in the dialog.</param>
            <param name="Details">The details to display in the dialog.</param>
            <param name="Severity">The severity of the message.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.ErrorDialog.Show(System.Exception,Tekla.Structures.Dialog.ErrorDialog.Severity)">
            <summary>
            Shows a new error dialog.
            </summary>
            <param name="Ex">The exception to display in the dialog.</param>
            <param name="Severity">The severity of the exception.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="Ex"/> parameter is null.</exception>
        </member>
        <member name="M:Tekla.Structures.Dialog.ErrorDialog.Show(System.String,System.Collections.Generic.List{System.String},Tekla.Structures.Dialog.ErrorDialog.Severity)">
            <summary>
            Shows a new error dialog.
            </summary>
            <param name="Message">The message to display in the dialog.</param>
            <param name="Details">The details to display in the dialog.</param>
            <param name="Severity">The severity of the message.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="Message"/> parameter is null.</exception>
        </member>
        <member name="F:Tekla.Structures.Dialog.ErrorDialog.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.ErrorDialog.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.ErrorDialog.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.ErrorDialog.Severity">
            <summary>
            The severity of the message to be displayed.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.ErrorDialog.Severity.INFO">
            <summary>
            The message is for informing.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.ErrorDialog.Severity.WARNING">
            <summary>
            The message is for warning.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.ErrorDialog.Severity.ERROR">
            <summary>
            The message is for notifying that an error has occurred.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.HelpViewer">
             <summary>
             Class for viewing help files with Tekla Structures HelpViewer.exe.
             </summary>
             <example>
             <code>
             using System;
             using Tekla.Structures.Dialog;
             
             public class Example
             {
                    public bool Example1()
                    {
                        // [*] Help root location is defined in registry key "HelpLocation". "HelpLocation" registry key's path is defined in HelpViewer.exe's configration file.
             
                        // Tekla Structures is running and file "example_help_topic" is located at
                        // location[*]\[Language] folder where [language] is active language in Tekla Structures.
                        HelpViewer.DisplayHelpTopic("example_help_topic");
            
                        // If Tekla Structures is not running, HelpViewer can be started independently by providing the path to the HelpViewer.exe, help topic and language.
                        HelpViewer.DisplayHelpTopicIndependent(@"D:\CustomPath\HelpViewer.exe", "example_help_topic", "enu");
                    }
             }
             </code>
             </example>
        </member>
        <member name="F:Tekla.Structures.Dialog.HelpViewer.HelpViewerExecutable">
            <summary>
            Name of the help viewer.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.HelpViewer.DefaultLanguage">
            <summary>
            Default language.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.HelpViewer.DisplayHelpTopic(System.String)">
            <summary>
            Method for opening Tekla Structures help viewer with given help topic.
            Method requires Tekla Structures to be running.
            </summary>
            <param name="helpTopic">Help topic (e.g. Help file name without extension).</param>
            <returns>True if success, otherwise false.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown when connection to Tekla Structures failed.</exception>
        </member>
        <member name="M:Tekla.Structures.Dialog.HelpViewer.DisplayHelpTopicIndependent(System.String,System.String,System.String)">
            <summary>
            Method for opening Tekla Structures help viewer independently from given file path with given help topic and language.
            </summary>
            <param name="helpViewerFilePath">Full file path to HelpViewer.exe containing the name of the executable.</param>
            <param name="helpTopic">Help topic (e.g. Help file name without extension).</param>
            <param name="language">Help language. Defaults to enu.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.HelpViewer.StartHelpViewer(System.String,System.String)">
            <summary>
            Method for starting help viewer with given path and arguments.
            </summary>
            <param name="helpViewerFilePath">Full file path to HelpViewer.exe containing the name of the executable.</param>
            <param name="arguments">Arguments to be used.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.MainWindow">
            <summary>
            The MainWindow class is a class for binding the .NET dialogs under the Tekla Structures main window.
            </summary>
            <example>
            This example shows how to use the <see cref="M:Tekla.Structures.Dialog.MainWindow.AddExternalWindow(System.String,System.IntPtr)"/> method to register a dialog
            to be insensitive while certain Tekla Structures modal dialogs are open.
            
            Unregistering is done similarly using the <see cref="M:Tekla.Structures.Dialog.MainWindow.RemoveExternalWindow(System.String,System.IntPtr)"/> method.
            <code>
            using System;
            using Tekla.Structures.Dialog;
            using Tekla.Structures.Model;
            
            public partial class Form1 : FormBase
            {
                   // Handles the form load event.
                   protected override void OnLoad(EventArgs e)
                   {
                       base.OnLoad(e);
                       MainWindow.Frame.AddExternalWindow(this.Name, this.Handle);
                   }
            
                   // Handles the form closed event.
                   protected override void OnClosed(EventArgs e)
                   {
                       MainWindow.Frame.RemoveExternalWindow(this.Name, this.Handle);
                       base.OnClosed(e);
                   }
            }
            </code>
            </example>
        </member>
        <member name="F:Tekla.Structures.Dialog.MainWindow.Frame">
            <summary>
            The main Tekla Structures window.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.MainWindow.#cctor">
            <summary>
            Creates a new main window instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.MainWindow.AddExternalWindow(System.String,System.IntPtr)">
            <summary>
            Registers the entire dialog to be insensitive at certain
            points, for example while modal Tekla Structures system dialog boxes
            are open. When insensitive, the dialog does not respond to any input.
            
            You can remove the registration of the dialog with <see cref="M:Tekla.Structures.Dialog.MainWindow.RemoveExternalWindow(System.String,System.IntPtr)"/>.
            </summary>
            <param name="Name">The unique name of the form.</param>
            <param name="Handle">The handle to the form that is to be added.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.MainWindow.RemoveExternalWindow(System.String,System.IntPtr)">
            <summary>
            Removes a registration done with <see cref="M:Tekla.Structures.Dialog.MainWindow.AddExternalWindow(System.String,System.IntPtr)"/>.
            </summary>
            <param name="Name">The unique name of the form.</param>
            <param name="Handle">The handle to the form that is to be removed.</param>
        </member>
        <member name="P:Tekla.Structures.Dialog.MainWindow.Handle">
            <summary>
            Gets the handle.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.TeklaProgressBar">
            <summary>The TeklaProgressBar class represents the common progress bar control for the Open API.</summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.TeklaProgressBar.#ctor(System.String)">
            <summary>Initializes the common progress bar control.</summary>
            <param name="text">The message to display in the control.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.TeklaProgressBar.TeklaProgressBar_Load(System.Object,System.EventArgs)">
            <summary>Used to translate progress bar and to set Tekla Standard properties.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.TeklaProgressBar.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.TeklaProgressBar.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.TeklaProgressBar.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.TeklaProgressBar.CancelClicked">
            <summary>The CancelClicked event is raised when the cancel button is clicked.</summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.TeklaProgressBar.Value">
            <summary>Gets or sets the current position of the progress bar.</summary>
             <remarks>The value should be a number between 0 and 100.</remarks>
        </member>
        <member name="P:Tekla.Structures.Dialog.TeklaProgressBar.Text">
            <summary>Gets or sets the current message text of the progress bar.</summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.AttributeInfo">
            <summary>
            Represents an attribute in the collection of the <see cref="T:Tekla.Structures.DialogInternal.AttributeManager"/>.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeInfo.BindValueControl(System.Windows.Forms.Control,System.String,System.Windows.Forms.DataSourceUpdateMode)">
            <summary>
            Binds the attribute value with the control.
            </summary>
            <param name="Ctrl">The control instance to bind.</param>
            <param name="PropertyName">The name of the control property to bind.</param>
            <param name="UpdateMode">When changes to the control property are propagated to the data source.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeInfo.BindFilterControl(System.Windows.Forms.Control,System.String,System.Windows.Forms.DataSourceUpdateMode)">
            <summary>
            Binds the attribute filter with the control.
            </summary>
            <param name="Ctrl">The control instance to bind.</param>
            <param name="PropertyName">The name of the control property to bind.</param>
            <param name="UpdateMode">When changes to the control property are propagated to the data source.</param>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeInfo.Name">
            <summary>
            Gets the name of the attribute.
            </summary>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeInfo.ValueType">
            <summary>
            Gets the type of the attribute value.
            </summary>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeInfo.Value">
            <summary>
            Gets or sets the attribute value.
            </summary>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeInfo.HasFilter">
            <summary>
            Gets a value indicating whether this attribute has a filter.
            </summary>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeInfo.FilterValue">
            <summary>
            Gets or sets the state of the filter (enabled/disabled).
            Returns null if the attribute does not have a filter.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Set filter value when attribute does not have filter.
            </exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null</exception>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.AttributeManager">
            <summary>
            Represents a collection of attributes that can be bound with Form controls.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.AddAttribute(System.String,System.Type,System.Boolean)">
            <summary>
            Add a new attribute and optionally create filter for it.
            An exception is thrown if attribute with identical name and different type exists.
            This method is safe to call multiple times with same attribute name and type.
            The subsequent method calls are no-op unless there is a need to create a filter for
            existing attribute.
            </summary>
            <param name="Name">The name of the attribute.</param>
            <param name="ReturnType">The type of the attribute.</param>
            <param name="CreateFilter">Create filter for the attribute.</param>
            <exception cref="T:System.InvalidOperationException">Attribute exists with different type.</exception>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.AddAttribute(System.String,System.Type)">
            <summary>
            Add a new attribute.
            An exception is thrown if attribute with identical name and different type exists.
            This method is safe to call multiple times with same attribute name and type.
            The subsequent method calls are no-op.
            </summary>
            <param name="Name">The name of the attribute.</param>
            <param name="ReturnType">The type of the attribute.</param>
            <exception cref="T:System.InvalidOperationException">Attribute exists with different type.</exception>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.ContainsAttribute(System.String)">
            <summary>
            Determines whether the AttributeManager contains the specified attribute.
            </summary>
            <param name="Name">The name of the attribute.</param>
            <returns>true if AttributeManager contains attribute with specified name; otherwise, false.</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.RemoveAttribute(System.String)">
            <summary>
            Removes the attribute with the specified name.
            </summary>
            <param name="Name">The name of the attribute.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.TryGetAttribute(System.String,Tekla.Structures.DialogInternal.AttributeInfo@)">
            <summary>
            Gets the attribute with the specified name.
            </summary>
            <param name="Name">The name of the attribute to get.</param>
            <param name="Attribute">
            When this method returns, contains the attribute with the specified name,
            if it exists; otherwise, null. This parameter is passed uninitialized.
            </param>
            <returns>
            true if the AttributeManager contains the attribute with the specified name;
            otherwise, false.
            </returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.UnbindControl(System.Windows.Forms.Control)">
            <summary>
            Removes all attribute bindings to the specified control.
            </summary>
            <param name="Ctrl">The control that is unbound.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.UnbindAllControls">
            <summary>
            Removes all attribute bindings that have been made.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.LoadXml(System.IO.Stream)">
            <summary>
            Loads the attribute values from the stream.
            </summary>
            <param name="FileStream">The stream to read.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.SaveXml(System.IO.Stream)">
            <summary>
            Saves the attribute values to the stream.
            </summary>
            <param name="FileStream">The stream to write.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeManager.OnAttributeRemoved(Tekla.Structures.DialogInternal.AttributeRemovedEventArgs)">
            <summary>
            Raises the <see cref="E:Tekla.Structures.DialogInternal.AttributeManager.AttributeRemoved"/> event.
            </summary>
            <param name="EventArgs">An <see cref="T:Tekla.Structures.DialogInternal.AttributeRemovedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeManager.Item(System.String)">
            <summary>
            Gets the attribute with the specified name.
            </summary>
            <param name="Name">The name of the attribute to get.</param>
            <returns>
            The attribute with the specified name. 
            If the specified name is not found a KeyNotFoundException is thrown.
            </returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="Name"/> is null</exception>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">No attribute exists with the specified name.</exception>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeManager.Attributes">
            <summary>
            Gets the attributes of the AttributeManager.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.AttributeRemovedEventArgs">
            <summary>
            Provides data for the <see cref="E:Tekla.Structures.DialogInternal.AttributeManager.AttributeRemoved"/> event.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.AttributeRemovedEventArgs.#ctor(System.String)">
            <summary>
            Initialized a new instance of the AttributeRemovedEventArgs.
            </summary>
            <param name="AttributeName">The name of the removed attribute.</param>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.AttributeRemovedEventArgs.AttributeName">
            <summary>
            Gets the name of the removed attribute.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.AttributeTypeNameEditor">
            <summary>
            The AttributeTypeNameEditor class provides a user interface for selecting a type for the bound attribute.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.AttributeTypeNameEditor.EditValue(System.ComponentModel.ITypeDescriptorContext,System.IServiceProvider,System.Object)">
            <summary>
            Edits the specified object's value using the editor style indicated by the GetEditStyle method.
            </summary>
            <param name="context">
            An ITypeDescriptorContext that can be used to gain additional context information.
            </param>
            <param name="provider">An IServiceProvider that the editor can use to obtain services.</param>
            <param name="value">The object to edit.</param>
            <returns>
            The new value of the object. If the value of the object has not changed, this should return
            the same object it was passed.
            </returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.AttributeTypeNameEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Gets the editor style used by the EditValue method.
            </summary>
            <param name="context">An ITypeDescriptorContext that can be used to gain additional context information.</param>
            <returns>
            A UITypeEditorEditStyle value that indicates the style of the editor used by the EditValue method.
            If the UITypeEditor does not support the method, then GetEditStyle will return None.
            </returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.BindPropertyNameEditor">
            <summary>
            The BindPropertyNameEditor class provides a user interface for selecting a property for the attribute binding.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.BindPropertyNameEditor.EditValue(System.ComponentModel.ITypeDescriptorContext,System.IServiceProvider,System.Object)">
            <summary>
            Edits the specified object's value using the editor style indicated by the GetEditStyle method.
            </summary>
            <param name="context">
            An ITypeDescriptorContext that can be used to gain additional context information.
            </param>
            <param name="provider">An IServiceProvider that the editor can use to obtain services.</param>
            <param name="value">The object to edit.</param>
            <returns>
            The new value of the object. If the value of the object has not changed, this should return
            the same object it was passed.
            </returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.BindPropertyNameEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Gets the editor style used by the EditValue method.
            </summary>
            <param name="context">An ITypeDescriptorContext that can be used to gain additional context information.</param>
            <returns>
            A UITypeEditorEditStyle value that indicates the style of the editor used by the EditValue method.
            If the UITypeEditor does not support the method, then GetEditStyle will return None.
            </returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.ApplicationFormBase">
            <summary>
            The ApplicationFormBase class is the base class for all Tekla Structures dialogs.
            The class provides localization, unit conversion and data storage
            (temporary and file-based) among other things.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.FormBase">
            <summary>
            The FormBase class is the base class for all Tekla Structures dialogs.
            The class provides localization, unit conversion and data storage
            (temporary and file-based) among other things.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.FormBase.structuresExtender">
            <summary>
            An extender for binding controls to data types. The bindings are needed to transfer dialog values
            to Tekla Structures and onwards to plug-ins.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.#ctor">
            <summary>
            The default constructor that does not need parameters.
            Initializes the form and registers property bindings.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources that are being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.InitializeForm">
            <summary>
            Prepares the data storage for the dialog and scans through the fields.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.InitializeDataStorage">
            <summary>
            Prepares ONLY the data storage.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.InitializeDistanceUnitDecimals">
            <summary>
            Gets the number of decimals Tekla Structures uses for displaying the "length" unit and
            initializes the distance datatype to use that.
            </summary>
            <returns>True if successful. False if failed.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.GetUnitDecimals(Tekla.Structures.Dialog.dotdiaUnitTypes_e)">
            <summary>
            Gets the number of decimals Tekla Structures uses with the specified unit type.
            </summary>
            <param name="unit">The specified unit type.</param>
            <returns>The number of decimals used, 0 (no decimals or invalid unit type) or -1 (failed).</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.GetConnectionStatus">
            <summary>
            Returns true if a proper connection to the Tekla Structures process has been established.
            If, for some reason, the connection has been lost, the method will return false. Currently,
            there's no way to re-establish the connection.
            </summary>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.ShowForm">
            <summary>
            Displays the form.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.Apply">
            <summary>
            The method to call when the Apply button is clicked. Stores the current dialog
            values to the dialog data storage.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.Modify">
            <summary>
            The method to call when the Modify button is clicked.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.RegisterPropertyBinding(System.Type,System.String)">
            <summary>
            Registers a binding for a control type. The bindings allow automatic formatting and
            field update events among other things. Needs to be called in the constuctor.
            </summary>
            <param name="type">The type of the control.</param>
            <param name="propertyName">The name of the property.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.RegisterPropertyBinding(System.Type,System.String,System.Windows.Forms.DataSourceUpdateMode)">
            <summary>
            Registers a binding for a field type. The bindings allow automatic formatting and
            field update events among other things.
            </summary>
            <param name="type">The type of the control.</param>
            <param name="propertyName">The name of the property.</param>
            <param name="updateMode">The event upon which to update the field.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.RegisterAttribute(System.String,System.Type)">
            <summary>
            Registers an attribute without binding to a control.
            </summary>
            <param name="AttributeName">The name of the attribute to be created.</param>
            <param name="ReturnType">The attribute type that will be returned.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.RegisterAttribute(System.String,System.Type,System.Boolean)">
            <summary>
            Registers an attribute without binding to a control and a filter.
            </summary>
            <param name="AttributeName">The name of the attribute to be created.</param>
            <param name="ReturnType">The attribute type that will be returned.</param>
            <param name="CreateFilter">The create filter for the attribute.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.RegisterFilterControl(System.String,System.Windows.Forms.Control)">
            <summary>
            Registers a control to filter an attribute.
            </summary>
            <param name="AttributeName">The name of the attribute to filter.</param>
            <param name="FilterControl">The control that will be the filter.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.ReadFromDataStorage(Tekla.Structures.DialogInternal.DialogDataStorageBase)">
            <summary>
            Gets the values from the given data storage and sets the values for the
            visible fields accordingly.
            </summary>
            <param name="dataStorage">The data storage to read from.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.WriteToDataStorage(Tekla.Structures.DialogInternal.DialogDataStorageBase)">
            <summary>
            Writes the values from the shown dialog to the given data storage.
            </summary>
            <param name="dataStorage">The data storage to write to.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.WriteToDataStack(Tekla.Structures.DialogInternal.DialogDataStorageBase)">
            <param name="dataStorage">The data storage to write to.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.UpdateValues">
            <summary>
            Rereads and updates all the field values on the form.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.ToggleSelection">
            <summary>
            Checks or unchecks all the enable check boxes.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.LoadValuesPath(System.String)">
            <summary>
            Retrieves the full path for the file name. Called from LoadValues.
            Override this function in order to change the default load path OR to add additional
            actions on LoadValues.
            </summary>
            <param name="FileName">The name of the file.</param>
            <returns>The full path together with the file name.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.ApplyValues(System.String)">
            <summary>
            Loads the dialog values from a file and performs <see cref="M:Tekla.Structures.Dialog.FormBase.Apply"/> on the loaded values.
            To match the files to a certain dialog, the file suffix is set as the dialog type's name.
            </summary>
            <param name="FileName">The name of the file.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.LoadValues(System.String)">
            <summary>
            Loads the dialog values from a file. To match the files to a certain dialog, 
            the file suffix is set as the dialog type's name.
            </summary>
            <param name="FileName">The name of the file.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.ModifyValues(System.String)">
            <summary>
            Loads the dialog values from a file and performs <see cref="M:Tekla.Structures.Dialog.FormBase.Modify"/> on the loaded values.
            To match the files to a certain dialog, the file suffix is set as the dialog type's name.
            </summary>
            <param name="FileName">The name of the file.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.LoadValues(System.String,Tekla.Structures.Dialog.FormBase.ApplyModifyDelegate)">
            <summary>
            Loads the dialog values from a file. To match the files to a certain dialog, 
            the file suffix is set as the dialog type's name.
            </summary>
            <param name="FileName">The name of the file.</param>
            <param name="applyModifyDelegate">The function to perform on the loaded values.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.LoadValuesFromStorageToStack(System.String,Tekla.Structures.DialogInternal.DialogDataStorageBase)">
            <summary>
            Loads the dialog values from a file. To match the files to a certain dialog, 
            the file suffix is set as the dialog type's name.
            </summary>
            <param name="FileName">The name of the file.</param>
            <param name="DataStorage">The datastorage of the dialog.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.SaveValuesPath(System.String)">
            <summary>
            Retrieves the full path for the file name. Called from SaveValues.
            Override this function in order to change the default save path OR to add additional
            actions on SaveValues.
            </summary>
            <param name="fileName">The name of the file.</param>
            <returns>The full path together with the file name.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.SaveValues(System.String)">
            <summary>
            Serializes the dialog values to an xml file.
            </summary>
            <param name="fileName">The path of the file.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.Windows.Forms.Control,System.Object)">
            <summary>
            Sets a value for the given control. When the dialog is not shown, setting a property
            directly for a control (such as textBox1.Text = "text") will not work for controls that
            have a Tekla Structures AttributeTypeName set. This method is going to have to be used
            to set the value.
            </summary>
            <param name="Ctrl">The control whose value to set.</param>
            <param name="Value">The new value. It can be of any primitive datatype supported by the
            control's AttributeType (normally int, double or string).</param>
            <example>
            The following example shows how to use <see cref="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.Windows.Forms.Control,System.Object)"/>:
            <code>
            using System;
            using System.Windows.Forms;
            using Tekla.Structures.Dialog;
            using Tekla.Structures.Dialog.UIControls;
            
            public class Example : PluginFormBase
            {
                   private void MaterialCatalogSelectionDone(object sender, EventArgs e)
                   {
                       materialTextBox.Text = materialCatalog.SelectedMaterial;
                       SetAttributeValue(materialTextBox, materialCatalog.SelectedMaterial);
                   }
            
                   private TextBox materialTextBox = new TextBox();
                   private MaterialCatalog materialCatalog = new MaterialCatalog();
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.String,System.Object)">
            <summary>
            Sets a value for the given attribute. This method is needed for attributes which are not
            bound to a control.
            </summary>
            <param name="AttributeName">The name of the attribute to be set.</param>
            <param name="Value">The new value set to the attribute.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.GetAttributeValue``1(System.Windows.Forms.Control)">
            <summary>
            Gets a value for the given Ctrl from the data storage. The return type can be set to
            a primitive type such as int, double or string.
            </summary>
            <typeparam name="T">The type of the returned variable.</typeparam>
            <param name="Ctrl">The control whose value to get.</param>
            <returns>The value set for the Ctrl in the requested datatype.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.GetAttributeValue``1(System.String)">
            <summary>
            Gets the value for the given attribute. This method is needed for attributes which are not
            bound to a control.
            </summary>
            <typeparam name="T">The type of the returned variable.</typeparam>
            <param name="AttributeName">The name of the attribute.</param>
            <returns>The value gotten from the attribute.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.RegisterAkitForm(System.String)">
            <summary>
            This is used ONLY for testing, MUST not be included in any release.
            Used for recording macros from .net applications.
            </summary>
            <param name="name"></param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBase.UnregisterAkitForm">
            <summary>
            This is used ONLY for testing, MUST not be included in any release.
            Used for recording macros from .net applications.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.FormBase.Localization">
            <summary>
            The localization instance for the dialog. Each dialog has its own localization instance
            that has read the localization files needed for that dialog.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.FormBase.DialogAttributeTypes">
            <summary>
            Gets the names and types of all the fields that have proper tags. The type is read from the tag.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.FormBase.FormInitialized">
            <summary>
            Run after the form has been initialized.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.ApplicationFormBase.#ctor">
            <summary>
            The default constructor that does not need parameters.
            Initializes the form and registers property bindings.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.ApplicationFormBase.DoLoad">
            <summary> It raises the Load event. </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.ApplicationFormBase.DoInitializeForm">
            <summary>
            Prepares the data storage for the dialog and scans through the fields.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.ApplicationFormBase.Apply">
            <summary>
            The method to call when the Apply button is clicked. Stores the current dialog
            values to the dialog data storage.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.StructuresDialogAttribute">
            <summary>
            The StructuresDialogAttribute class specifies the attribute name to which the value of the
            property will be bound in Tekla Structures.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresDialogAttribute.#ctor(System.String)">
            <summary>
            Initializes a new StructuresDialogAttribute instance.
            </summary>
            <param name="attributeName">The attribute name of the property.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresDialogAttribute.ToString">
            <summary>
            Returns a string that represents the current StructuresDialogAttribute object.
            </summary>
            <returns>A string that represents the current StructuresDialogAttribute object.</returns>
        </member>
        <member name="P:Tekla.Structures.Dialog.StructuresDialogAttribute.AttributeName">
            <summary>
            Gets the attribute name of the property.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.StructuresDialogFilterAttribute">
            <summary>
            The StructuresDialogFilterAttribute class specifies the attribute name for which the
            value of the attributed property will be used as a filter in Tekla Structures. The return
            type of the attributed property must be boolean. The apply and modify operations will only
            commit/update those attributes which either do not have a filter property defined or the
            value of the filter property is true.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresDialogFilterAttribute.#ctor(System.String)">
            <summary>
            Initializes a new StructuresDialogFilterAttribute instance.
            </summary>
            <param name="attributeName">The attribute name of the attributed property.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresDialogFilterAttribute.ToString">
            <summary>
            Returns a string that represents the current StructuresDialogFilterAttribute object.
            </summary>
            <returns>A string that represents the current StructuresDialogFilterAttribute object.</returns>
        </member>
        <member name="P:Tekla.Structures.Dialog.StructuresDialogFilterAttribute.AttributeName">
            <summary>
            Gets the attribute name of the property.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.StructuresDialogArrayAttribute">
            <summary>
            The StructuresDialogArrayAttribute class specifies the attribute name for which the attributed
            interface will be used as a list item definition. The attributed interface is used to define the
            structure of the DataGridView items. In other words it defines all the possible DataGridView
            columns. The interface is bound to the DataGridView by referencing the interface's attribute name
            from the DataGridView's Tag property.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresDialogArrayAttribute.#ctor(System.String)">
            <summary>
            Initializes a new StructuresDialogArrayAttribute instance.
            </summary>
            <param name="attributeName">The attribute name of the attributed interface.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresDialogArrayAttribute.ToString">
            <summary>
            Returns a string that represents the current StructuresDialogArrayAttribute object.
            </summary>
            <returns>A string that represents the current StructuresDialogArrayAttribute object.</returns>
        </member>
        <member name="P:Tekla.Structures.Dialog.StructuresDialogArrayAttribute.AttributeName">
            <summary>
            Gets the attribute name of the interface.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.DialogDataStorageBase">
            <summary>
            The DialogDataStorageBase class is the base class for data storages of FormBase dialogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorageBase.GetVariable(Tekla.Structures.DialogInternal.AttributeInfo)">
            <summary>
            Gets the value of the named variable.
            </summary>
            <param name="info">The AttributeInfo struct that contains the variable information.</param>
            <returns>The value of the requested variable.</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorageBase.SetVariable(Tekla.Structures.DialogInternal.AttributeInfo)">
            <summary>
            Sets a value for the named variable.
            </summary>
            <param name="info">The AttributeInfo struct that contains the variable information.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorageBase.SetVariableToStack(System.String,System.Object)">
            <summary>
            Sets values for the variables in the data storage to stack.
            </summary>
            <param name="name">The name of the variable.</param>
            <param name="data">The value of the variable.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorageBase.InitializeLifetimeService">
            <summary>
            An empty override method required by the System.MarshalByRefObject base class.
            </summary>
            <returns>Null always.</returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.Dialogs">
            <summary>
            The Dialogs class contains interface methods for Tekla Structures to handle FormBase dialogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.GetPluginFormBaseByPluginFormName(System.String)">
            <summary>
            Retrieves an instance of the form.
            </summary>
            <param name="PluginFormName">The complete name of the plug-in form (including the plug-in name).</param>
            <returns>The existing PluginFormBase or null if it did not exist.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.LoadDialogs(System.String)">
            <summary>
            Reads the PluginFormBase dialogs from all found dll files in bin\dialogs and bin\plugins.
            </summary>
            <param name="Param">Not used.</param>
            <returns>0 always. The return value is not useful because it is not sent to the Tekla Structures core.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.GetAttributeFileNameAndDialogName(System.String@,System.String@)">
            <summary>
            Since AKIT doesn't allow us to transport any parameters directly, we have to fetch them from Core at this point.
            </summary>
            <param name="AttributeFileName">The name of the attribute file.</param>
            <param name="PluginFormName">The full name of the dialog (should contain Plugin name)</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.LoadAttributeFileNameToDialogAndApply(System.String)">
            <summary>
            Uses the attributes found from the attribute file as the applied values for the specified dialog.
            </summary>
            <param name="Param">Not used.</param>
            <returns>0 always. The return value is not useful because it is not sent to the Tekla Structures core.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.LoadAttributeFileNameToDialogAndModify(System.String)">
            <summary>
            Uses the attributes found from the attribute file as the current values for the specified dialog.
            Modifies the current selected instance. Applied values for this dialog are not changed.
            </summary>
            <param name="Param">Not used.</param>
            <returns>0 always. The return value is not useful because it is not sent to the Tekla Structures core.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.LoadAttributeFileNameToDialogAndApply(System.String,System.String)">
            <summary>
            Loads the specified dialog's applied values from the attribute file.
            </summary>
            <param name="AttributeFileName">The name of the attribute file.</param>
            <param name="PluginFormName">The full name of the dialog.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.LoadAttributeFileNameToDialogAndModify(System.String,System.String)">
            <summary>
            Loads the specified dialog's current values from the attribute file and modifies the current selected instance.
            </summary>
            <param name="AttributeFileName">The name of the attribute file.</param>
            <param name="PluginFormName">The full name of the dialog.</param>
            <returns>True on success.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.ReloadDialogs(System.String)">
            <summary>
            Reloads all PluginFormBase dialogs with default/standard values.
            </summary>
            <param name="param">Not used.</param>
            <returns>0 always.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.SetSettings(System.String)">
            <summary>
            Forces the dialog system to get the language and localization settings from Tekla Structures.
            </summary>
            <param name="param">Not used.</param>
            <returns>0 always.</returns>
        </member>
        <member name="F:Tekla.Structures.Dialog.Dialogs._LastOpenedDialog">
            <summary>
            Because we don�t support multiple dialog we need to save the last opened dialog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.OpenDialog(System.String)">
            <summary>
            Opens a dialog. The name of the dialog is asked from the Tekla Structures core.
            </summary>
            <param name="Param">Not used.</param>
            <returns>0 always.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.OpenDialogAndGet(System.String)">
            <summary>
            Opens a dialog and forces it to get the values from the currently selected part.
            </summary>
            <param name="param">Not used.</param>
            <returns>0 always.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.LoadAttributeFileToStack(System.String)">
            <summary>
            Opens a dialog and forces it to get the values from the currently selected part.
            </summary>
            <param name="param">Not used.</param>
            <returns>0 always.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.PerformUpdateValues(Tekla.Structures.Dialog.Dialogs.ExistingDialog)">
            <summary>
            Method for performing dialog form update values
            </summary>
            <param name="dialog">Existing dialog.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.PerformRefreshForm(Tekla.Structures.Dialog.Dialogs.ExistingDialog)">
            <summary>
            Method for performing dialog form refresh when opened while already open.
            </summary>
            <param name="dialog">Existing dialog.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Dialogs.PerformDialogFormGet(Tekla.Structures.Dialog.Dialogs.ExistingDialog)">
            <summary>
            Method for getting dialog values.
            </summary>
            <param name="dialog">Existing dialog.</param>
        </member>
        <member name="T:Tekla.Structures.Dialog.FilteredListBox">
            <summary>
            Represents a control to display a filtered list of items.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FilteredListBox.#ctor">
            <summary>
            Initializes a new instance of the FilteredListBox class.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.FilteredListBox.Items">
            <summary>
            Gets or sets the items of the FilteredListBox.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.FilteredListBox.SelectedItem">
            <summary>
            Gets or sets the currently selected item in the FilteredListBox.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.FilteredListBox.ItemSelectionDone">
            <summary>
            Occurs when the item selection is done.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FilteredListBox.FilteredItemComparer.Compare(System.String,System.String)">
             <summary>
             Compares two string instances and returns a value indicating whether one is less than,
             equal to, or greater than the other.
             A string equal to the constructor's selectedItem parameter is considered to be less than
             anything else. The strings that start with the constructor's filter parameter will be next
             and they are followed by the strings that contain (but do not start with) the filter parameter.
             The strings that do not contain filter parameter will be last.
            
             Each group is internally sorted with string.Sort(x, y, StringComparison.InvariantCultureIgnoreCase).
            
             All comparisons are case-insensitive.
             </summary>
             <param name="x">The first string instance to compare.</param>
             <param name="y">The second string instance to compare.</param>
             <returns>
             Less than zero if x is less than y. Zero if x equals y. Greater than zero if x is greater than y.
             </returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.FormBorders">
            <summary>
            The FormBorders class handles the storing and restoring of the form size and location.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBorders.StoreFormSizeAndLocation(System.Windows.Forms.Form)">
            <summary>
            Stores the form size and location into the registry.
            </summary>
            <param name="form">The form in question.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.FormBorders.RestoreFormSizeAndLocation(System.Windows.Forms.Form)">
            <summary>
            Restores the form size and location from the registry.
            </summary>
            <param name="form">The form in question.</param>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.LocalDataStorage">
            <summary>
            Local data storage for temporarily storing dialog values
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.LocalDataStorage.GetVariable(Tekla.Structures.DialogInternal.AttributeInfo)">
            <summary>
            Get a variable by the give name
            </summary>
            <param name="info">The AttributeInfo struct that contains the variable information.</param>
            <returns>The value of the requested variable</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.LocalDataStorage.SetVariable(Tekla.Structures.DialogInternal.AttributeInfo)">
            <summary>
            Set a value for a variable
            </summary>
            <param name="info">The AttributeInfo struct that contains the variable information.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.LocalDataStorage.SetVariableToStack(System.String,System.Object)">
            <summary>
            Sets a value for the named variable to stack.
            </summary>
            <param name="name">The name of the variable.</param>
            <param name="data">The value of the variable.</param>
        </member>
        <member name="T:Tekla.Structures.Dialog.Localization">
            <summary>
            The Localization class is for translating strings in .NET dialogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.#ctor">
            <summary>
            Creates a new localization instance.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.#ctor(System.String,System.String)">
            <summary>
            Creates a new localization instance.
            </summary>
            <param name="fileName">The file from which the localization strings are loaded.</param>
            <param name="language">The set localization language.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.LoadFile(System.String)">
            <summary>
            Loads the localization strings from a file. Several files may be used concurrently, 
            just call LoadFile for each file. The translations are searched in the loading order and the first match is returned.
            The method uses the extension of the filename to identify the file type 
            (".xml", ".aid" or ".ail").
            </summary>
            <param name="fileName">The file to be loaded.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.LoadAidFile(System.String)">
            <summary>
            Loads the localization strings from an aid file. Several files may be used concurrently,
            just call LoadAidFile for each file. The translations are searched in the loading order and the first match is returned.
            </summary>
            <param name="fileName">The aid file to be loaded.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.LoadAilFile(System.String)">
            <summary>
            Loads the localization strings from an ail file. Several files may be used concurrently,
            just call LoadAilFile for each file. The translations are searched in the loading order and the first match is returned.
            </summary>
            <param name="fileName">The ail file to be loaded.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.LoadXMLFile(System.String)">
            <summary>
            Loads the localization strings from an xml file. Several files may be used concurrently,
            just call LoadXMLFile for each file. The translations are searched in the loading order and the first match is returned.
            </summary>
            <param name="fileName">The xml file to be loaded.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.RegisterLocalizationCallback(Tekla.Structures.Dialog.Localization.LocalizationCallback,System.Type[])">
            <summary>
            Registers a localization callback which is used to translate the registered control types.
            </summary>
            <param name="cb">The localization delegate.</param>
            <param name="types">An array of types which will trigger the current delegate.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.LocalizeToolTip(System.Windows.Forms.Control,System.Windows.Forms.ToolTip)">
            <summary>
            Localizes a ToolTip control.
            </summary>
            <param name="control">The root control for all the controls which should be localized.</param>
            <param name="toolTip">The ToolTip instance.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Localize(System.Windows.Forms.MenuItem)">
            <summary>
            Localizes a MenuItem control recursively.
            </summary>
            <param name="menuItem">The MenuItem instance.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Localize(System.Windows.Forms.Control)">
            <summary>
            Localizes a control recursively.
            </summary>
            <param name="control">The control instance.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Localize(System.Configuration.ApplicationSettingsBase)">
            <summary>
            Localizes application settings.
            </summary>
            <param name="applicationSettings">The application settings that should be localized.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.GetText(System.String)">
            <summary>
            Gets the translation for the current language. 
            The translations are searched with the given identifier string.
            </summary>
            <param name="name">The identifier string.</param>
            <returns>The translated string.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.GetString(System.String,System.String)">
            <summary>
            Gets the translation for the given language.
            The translations are searched with the given identifier string.
            </summary>
            <param name="name">The identifier string.</param>
            <param name="language">The target language.</param>
            <returns>The translated string.</returns>
        </member>
        <member name="P:Tekla.Structures.Dialog.Localization.DefaultLocalizationFile">
            <summary>
            The path and name of the default localization file.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.Localization.DefaultLocalizationPath">
            <summary>
            The path and directory name where to find localization files.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.Localization.Language">
            <summary>
            Gets or sets the language that is currently used in Tekla Structures.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.Localization.LocalizationCallback">
            <summary>
            A delegate for localization functions for different control types.
            </summary>
            <param name="localization">The localization instance to be used.</param>
            <param name="obj">The object to be localized.</param>
        </member>
        <member name="T:Tekla.Structures.Dialog.Localization.Util">
            <summary>
            The Util class contains general localizers for different kinds of controls.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Util.LocalizeListControl(Tekla.Structures.Dialog.Localization,System.Object)">
            <summary>
            Localizes a List control.
            </summary>
            <param name="localization">The localization instance to be used.</param>
            <param name="obj">The object to be localized.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Util.LocalizeDataGridView(Tekla.Structures.Dialog.Localization,System.Object)">
            <summary>
            Localizes a DataGridView.
            </summary>
            <param name="localization">The localization instance to be used.</param>
            <param name="obj">The object to be localized.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Util.LocalizeToolStrip(Tekla.Structures.Dialog.Localization,System.Object)">
            <summary>
            Localizes a ToolStrip.
            </summary>
            <param name="localization">The localization instance to be used.</param>
            <param name="obj">The object to be localized.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Util.LocalizeImageListComboBox(Tekla.Structures.Dialog.Localization,System.Object)">
            <summary>
            Localizes an ImageListComboBox.
            </summary>
            <param name="localization">The localization instance to be used.</param>
            <param name="obj">The object to be localized.</param>
        </member>
        <member name="T:Tekla.Structures.Dialog.Localization.Util.PropertyLocalizer">
            <summary>
            The PropertyLocalizer class localizes properties.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Util.PropertyLocalizer.#ctor(System.String)">
            <summary>
            Creates a new instance of the localizer.
            </summary>
            <param name="propertyName">The name of the property.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.Localization.Util.PropertyLocalizer.Localize(Tekla.Structures.Dialog.Localization,System.Object)">
            <summary>
            Localizes the object with the given localization instance.
            </summary>
            <param name="localization">The localization instance to be used.</param>
            <param name="obj">The object to be localized.</param>
        </member>
        <member name="T:Tekla.Structures.Dialog.PluginFormBase">
            <summary>
            The PluginFormBase class is the base class for plug-in dialogs. The class extends
            the FormBase class by adding communications with Tekla Structures.
            </summary>
            <example>
            The following example shows a situation where a class is created by using inheritance from the PluginFormBase. Due to limited space a 
            better example is located in the Open API Start-up Package in Extranet.  The name of the example is FormPlugin.
            <code>
            using System;
            using System.Collections.Generic;
            using System.Drawing;
            using System.Text;
            using System.Windows.Forms;
            
            using Tekla.Structures.Datatype;
            using Tekla.Structures.Dialog;
            
            public class Example1 : PluginFormBase
            {
                private void InitializeComponent()
                {
                    this.OkButton = new System.Windows.Forms.Button();
                    this.ModifyButton = new System.Windows.Forms.Button();
                    this.SuspendLayout();
                    // 
                    // OkButton
                    // 
                    this.structuresExtender.SetAttributeName(this.OkButton, null);
                    this.structuresExtender.SetAttributeTypeName(this.OkButton, null);
                    this.structuresExtender.SetBindPropertyName(this.OkButton, null);
                    this.OkButton.Name = "OkButton";
                    this.OkButton.Text = "OK";
                    this.OkButton.Click += new System.EventHandler(this.OkButton_Click);
                    // 
                    // ModifyButton
                    // 
                    this.structuresExtender.SetAttributeName(this.ModifyButton, null);
                    this.structuresExtender.SetAttributeTypeName(this.ModifyButton, null);
                    this.structuresExtender.SetBindPropertyName(this.ModifyButton, null);
                    this.ModifyButton.Name = "ModifyButton";
                    this.ModifyButton.Text = "Modify";
                    this.ModifyButton.Click += new System.EventHandler(this.ModifyButton_Click);
                    // 
                    // Example1
                    // 
                    this.structuresExtender.SetAttributeName(this, null);
                    this.structuresExtender.SetAttributeTypeName(this, null);
                    this.structuresExtender.SetBindPropertyName(this, null);
                    this.Controls.Add(this.ModifyButton);
                    this.Controls.Add(this.OkButton);
                    this.Name = "Example1";
                    this.ResumeLayout(false);
                }
            
                public Example1()
                {
                    InitializeComponent();
                }
            
                private void OkButton_Click(object sender, EventArgs e)
                {
                    this.Apply();
                    this.Close();
                }
            
                private void ModifyButton_Click(object sender, EventArgs e)
                {
                    this.Modify();
                }
            
                private System.Windows.Forms.Button OkButton;
                private System.Windows.Forms.Button ModifyButton;
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.#ctor">
            <summary>
            Runs the FormBase constructor and loads the default .NET localization file (DotNetDialogStrings.ail).
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.SetTemporaryDataStorage(Tekla.Structures.DialogInternal.DialogDataStorage)">
            <summary>
            Sets the data storage class that communicates with Tekla Structures.
            Handled by the Tekla.Structures.Dialog classes.
            </summary>
            <param name="Storage">The data storage class.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.SetModelDataStorage(Tekla.Structures.DialogInternal.DialogDataStorage)">
            <summary>
            Sets the data storage class that communicates with Tekla Structures.
            Handled by the Tekla.Structures.Dialog classes.
            </summary>
            <param name="Storage">The data storage class.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.DoLoad">
            <summary> It raises the Load event. </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.DoInitializeForm">
            <summary>
            Once per Tekla Structures execution registers the dialog and its fields to the Tekla Structures
            Object Dialog tree. Loads the initial values from the standard file.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.ReloadForm">
            <summary>
             Reloads the dialog values.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.Apply">
            <summary>
            The method to call when the Apply button is clicked. Stores the current dialog values locally and 
            to the Tekla Structures Object Dialog tree.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.Modify">
            <summary>
            The method to call when the Modify button is clicked. Modifies the part that is currently selected in
            Tekla Structures. First disables all the toggles in the object dialog tree, then enables the correct
            ones by setting their values in WriteToDataStorage.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.Get">
            <summary>
            Gets the dialog values from the part that is currently selected in Tekla Structures.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.LoadValuesPath(System.String)">
            <summary>
            Retrieves the full path for the file name. Called from LoadValues.
            Override this function in order to change the default load path OR to add additional
            actions on LoadValues.
            </summary>
            <param name="FileName">The name of the file.</param>
            <returns>The full path together with the file name.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.LoadValuesToStack(System.String)">
            <summary>
            Loads the dialog values from a file. To match the files to a certain dialog, 
            the file suffix is set as the dialog type's name.
            </summary>
            <param name="FileName">The name of the file.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.PluginFormBase.SaveValuesPath(System.String)">
            <summary>
            Retrieves the full path for the file name. Called from SaveValues.
            Override this function in order to change the default save path OR to add additional
            actions on SaveValues.
            </summary>
            <param name="FileName">The name of the file.</param>
            <returns>The full path together with the file name.</returns>
        </member>
        <member name="E:Tekla.Structures.Dialog.PluginFormBase.AttributesLoadedFromModel">
            <summary>
            The AttributesLoadedFromModel event is triggered just after the attributes have been loaded
            from the model into the dialog.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.PluginFormBase.ShowInTaskbar">
            <summary>
            Hides (shadows) the ShowInTaskbar property by setting the property to false.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.ProfileConversion">
            <summary>
            The ProfileConversion class provides functionalities to convert profile strings from/to current units (set in
            "Units and decimals, Catalog, Profiles, Section Dimension" options).
            </summary>
            <example>
            The next example assumes that the option "Units and decimals, Catalog, Profiles, Section Dimension" 
            is set to "in".
            <code>
            using Tekla.Structures.Dialog;
            
            class Example
            {
                public Example()
                {
                    string Profile = "PL10*10"; // Or "PL10\"*10\""
                    string ConvertedProfile = string.Empty;
            
                    ProfileConversion.ConvertFromCurrentUnits(Profile, ref ConvertedProfile);
                    // ConvertedProfile will contain "PL254*254"
            
                    Profile = "PL508*25.4";
                    ProfileConversion.ConvertToCurrentUnits(Profile, ref ConvertedProfile);
                    // The converted profile will contain "PL20\"*1\""
                }
            }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Dialog.ProfileConversion.ConvertFromCurrentUnits(System.String,System.String@)">
            <summary>
            Converts a profile string from current units to internal units (set in
            Units and decimals options).
            </summary>
            <param name="Profile">The profile string to convert.</param>
            <param name="ConvertedProfile">The converted profile string.</param>
            <returns>True if successfully converted the string.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.ProfileConversion.ConvertToCurrentUnits(System.String,System.String@)">
            <summary>
            Converts a profile string from internal units to current units (set in
            Units and decimals options).
            </summary>
            <param name="Profile">The profile string to convert.</param>
            <param name="ConvertedProfile">The converted profile string.</param>
            <returns>True if successfully converted the string.</returns>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.CDelegateSynchronized">
            <summary>
            Provides akit synchronization to CDialogDelegate.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.ICDelegate">
            <summary>
            
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportAddExternalWindow(System.String,System.IntPtr)">
            <summary>
            Adds an exernal window as TS child
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportRemoveExternalWindow(System.String,System.IntPtr)">
            <summary>
            Removes an external child from TS.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportRegisterDialog(System.String)">
            <summary>
            Register a FormBase-based dialog to the object dialog tree
            </summary>
            <param name="Name">The name the dialog</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetModelPath(System.String@)">
            <summary>
            Get path to the currently open model's directory
            </summary>
            <param name="Path">The returned path</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportAddAttribute(Tekla.Structures.DialogInternal.dotdiaAttributeDefinition_t@)">
            <summary>
            Add an attribute for the current dialog
            </summary>
            <param name="Attribute">The name and description of the attribute</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportStoreAppliedValues">
            <summary>
            Copy current dialog values to applied values for the dialog
            </summary>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetCB">
            <summary>
            The dialog Get button callback. Gets the values for the dialog from the currently selected part
            and sets them as the "dialog values" in the object dialog tree
            </summary>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportModifyCB">
            <summary>
            The dialog Modify button callback. Gets the "dialog values" from the object dialog tree and
            sets them for the currently selected part.
            </summary>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportFindAttributeFile(System.String,System.String@)">
            <summary>
            Find a saved dialog data file. Search in the model, project, firm and system folders in this order
            </summary>
            <param name="pFileName">The name of the dialog data file</param>
            <param name="FullFilePath">The full path to the found file</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportSetActiveDialog(System.String)">
            <summary>
            Set active dialog by name
            </summary>
            <param name="Name">The name of the dialog</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportSetDialogToggles(System.Int32)">
            <summary>
            Set the toggles for the current dialog's attributes on or off
            </summary>
            <param name="ToggleValue">true (1) to enable toggles, false (0) to disable</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportSetDialogValue(System.String,Tekla.Structures.DialogInternal.dotdiaAttributeValue_t@)">
            <summary>
            Set a value to an attribute, by attribute name, in the current dialog
            </summary>
            <param name="Name">The name of the attribute</param>
            <param name="Value">The value structure for the attribute</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetDialogValue(System.String,Tekla.Structures.DialogInternal.dotdiaAttributeValue_t@)">
            <summary>
            Get a value of an attribute, by attribute name, in the currently dialog
            </summary>
            <param name="Name">The name of the attribute</param>
            <param name="Value">The value structure for the attribute</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetUnitDecimals(Tekla.Structures.Dialog.dotdiaUnitTypes_e)">
            <summary>
            Get the number of decimals for a specified unit.
            </summary>
            <param name="UnitType">The unit type</param>
            <returns>The number of decimals for this unit</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetSettings(Tekla.Structures.DialogInternal.dotdiaSettings_t@)">
            <summary>
            Get the settings structure
            </summary>
            <param name="Settings">The settings structure to fill</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetActiveDialog(System.String@)">
            <summary>
            Get the active dialog name. The active dialog has to be set with dotdiaSetDialog.
            </summary>
            <param name="Name">The name of the dialog will be set here</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetAttributeFileName(System.String@)">
            <summary>
            Get the attribute file name for laoading to stack. 
            </summary>
            <param name="Name">The name of the file will be set here</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportAddAttributeToStack(System.String,Tekla.Structures.DialogInternal.dotdiaAttributeValue_t@)">
            <summary>
            Set a value to an attribute, by attribute name, in the current dialog to the stack
            </summary>
            <param name="Name">The name of the attribute</param>
            <param name="Value">The value structure for the attribute</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportConvertFromCurrentUnits(Tekla.Structures.DialogInternal.dotdiaProfile_t@)">
            <summary>
            Converts the input profile string from current units (defined in Unit and decimals options).
            </summary>
            <param name="Profile">Profile string to convert</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportConvertToCurrentUnits(Tekla.Structures.DialogInternal.dotdiaProfile_t@)">
            <summary>
            Converts the input profile string to current units (defined in Unit and decimals options).
            </summary>
            <param name="Profile">Profile string to convert</param>
            <returns>true (1) on success, false(0) otherwise</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportAddAkitForm(Tekla.Technology.Akit.IAkitForm@)">
            <summary>
            Adds application form to akit to be used for recording macros from application
            </summary>
            <param name="akitForm"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportRemoveAkitForm(Tekla.Technology.Akit.IAkitForm@)">
            <summary>
            Removes application form from akit
            </summary>
            <param name="akitForm"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.ExportGetMainFrameHandle(System.Int32@)">
            <summary>
            Gets main frame handle from TS
            </summary>
            <param name="Ptr"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.LoadAttributeFileNameToDialogAndApply(System.String,System.String)">
            <summary>
            Applies the attributes found from the attribute file to the specified Dialog.
            </summary>
            <param name="pAttributeFileName"></param>
            <param name="pDialogName"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.LoadAttributeFileNameToDialogAndModify(System.String,System.String)">
            <summary>
            Loads the attributes found from the attribute file to the specified Dialog and modify current selected instance.
            </summary>
            <param name="pAttributeFileName"></param>
            <param name="pDialogName"></param>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.ICDelegate.DisplayHtmlHelpTopic(System.String)">
            <summary>
            Launches the html help viewer with given topic.
            </summary>
            <param name="pHelpTopic">Help topic.</param>
            <returns>True on success, false otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.#ctor">
            <summary>
            Constructor for the synchronization object
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.BeginInvoke(System.Delegate,System.Object[])">
            <summary>
            Begininvoke starts the function call
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(System.IAsyncResult)">
            <summary>
            Endinvoke waits for the call to finnish.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.Invoke(System.Delegate,System.Object[])">
            <summary>
            Invoke begins and ends the call.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(System.IntPtr@,System.String,System.IAsyncResult)">
            <summary>
            Get Akit mainframe handle
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(System.Int32,System.IAsyncResult)">
            <summary>
            Int parameter
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(System.String,System.IAsyncResult)">
            <summary>
            String parameter
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(System.Text.StringBuilder,System.IAsyncResult)">
            <summary>
            StringBuilder parameter
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.DialogInternal.dotdiaAttributeDefinition_t@,System.IAsyncResult)">
            <summary>
            AttributeDefinition parameter
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(System.String,System.Text.StringBuilder,System.IAsyncResult)">
            <summary>
            String and StringBuilder parameters
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(System.String,Tekla.Structures.DialogInternal.dotdiaAttributeValue_t@,System.IAsyncResult)">
            <summary>
            String and AttributeValue parameters
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.DialogInternal.dotdiaSettings_t@,System.IAsyncResult)">
            <summary>
            Settings parameter
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.EndInvoke(Tekla.Structures.DialogInternal.dotdiaProfile_t@,System.IAsyncResult)">
            <summary>
            Profile parameter
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportAddExternalWindow(System.String,System.IntPtr)">
            <summary>
            Register window to AKIT.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportRemoveExternalWindow(System.String,System.IntPtr)">
            <summary>
            Unregisters window to AKIT.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportRegisterDialog(System.String)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetModelPath(System.String@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportAddAttribute(Tekla.Structures.DialogInternal.dotdiaAttributeDefinition_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportStoreAppliedValues">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetCB">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportModifyCB">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportFindAttributeFile(System.String,System.String@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportSetActiveDialog(System.String)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportSetDialogToggles(System.Int32)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportSetDialogValue(System.String,Tekla.Structures.DialogInternal.dotdiaAttributeValue_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetDialogValue(System.String,Tekla.Structures.DialogInternal.dotdiaAttributeValue_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetUnitDecimals(Tekla.Structures.Dialog.dotdiaUnitTypes_e)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetSettings(Tekla.Structures.DialogInternal.dotdiaSettings_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetActiveDialog(System.String@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportAddAttributeToStack(System.String,Tekla.Structures.DialogInternal.dotdiaAttributeValue_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetAttributeFileName(System.String@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportConvertFromCurrentUnits(Tekla.Structures.DialogInternal.dotdiaProfile_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportConvertToCurrentUnits(Tekla.Structures.DialogInternal.dotdiaProfile_t@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportAddAkitForm(Tekla.Technology.Akit.IAkitForm@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportRemoveAkitForm(Tekla.Technology.Akit.IAkitForm@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.ExportGetMainFrameHandle(System.Int32@)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.LoadAttributeFileNameToDialogAndApply(System.String,System.String)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.LoadAttributeFileNameToDialogAndModify(System.String,System.String)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegateSynchronized.DisplayHtmlHelpTopic(System.String)">
            <summary> DO NOT USE! For internal usage only! </summary>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.CDelegateSynchronized.InvokeRequired">
            <summary>
            Are we already running in a-kit thread?
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.dotdiaUnitTypes_e">
            <summary>
            Specifies the Tekla Structures unit types.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_BOOLEAN">
            <summary>The input boolean.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_INTEGER">
            <summary>The input integer.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_STRING">
            <summary>The input string.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_NONE">
            <summary>The input none.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_RATIO_UNIT">
            <summary>The input ratio unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_STRAIN_UNIT">
            <summary>The input strain unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_ANGLE_UNIT">
            <summary>The input angle unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_ANGLE_UNIT">
            <summary>The output angle unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_SECTION_ANGLE_UNIT">
            <summary>The input section angle unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_LENGTH_UNIT">
            <summary>The input length unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_LENGTH_UNIT">
            <summary>The output length unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DEFORMATION_UNIT">
            <summary>The input deformation unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_DEFORMATION_UNIT">
            <summary>The output deformation unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DIMENSION_UNIT">
            <summary>The input dimension unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_RADIUSOFINERTIA_UNIT">
            <summary>The input radius of inertia unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_AREA_UNIT">
            <summary>The input area unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_REINFAREA_UNIT">
            <summary>The output reinforced area unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_TRANSVREINF_UNIT">
            <summary>The output transverse reinforcement unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_AREAPERLENGTH_UNIT">
            <summary>The input area per length unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_VOLUME_UNIT">
            <summary>The output volume unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_SECTIONMODULUS_UNIT">
            <summary>The input section modulus unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_VOLUME_UNIT">
            <summary>The input volume unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_MOMENTOFINERTIA_UNIT">
            <summary>The input moment of inertia unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_TORSIONCONSTANT_UNIT">
            <summary>The input torsion constant unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_WARPINGCONSTANT_UNIT">
            <summary>The input warping constant unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_FORCE_UNIT">
            <summary>The input force unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_FORCE_UNIT">
            <summary>The output force unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_WEIGHT_UNIT">
            <summary>The input weight unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_WEIGHT_UNIT">
            <summary>The output weight unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DISTRIBLOAD_UNIT">
            <summary>The input distributed load unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_DISTRIBLOAD_UNIT">
            <summary>The output distributed load unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_SPRINGCONSTANT_UNIT">
            <summary>The input spring constant unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_MASSPERLENGTH_UNIT">
            <summary>The output mass per length unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_SURFACELOAD_UNIT">
            <summary>The input surface load unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_SURFACELOAD_UNIT">
            <summary>The output surface load unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_STRENGTH_UNIT">
            <summary>The input strength unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_STRESS_UNIT">
            <summary>The output stress unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_MODULUS_UNIT">
            <summary>The input modulus unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DENSITY_UNIT">
            <summary>The input density unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_MOMENT_UNIT">
            <summary>The input moment unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_MOMENT_UNIT">
            <summary>The output moment unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DISTRIBMOMENT_UNIT">
            <summary>The input distributed moment unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_ROTSPRINGCONST_UNIT">
            <summary>The input rotation spring constant unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_TEMPERATURE_UNIT">
            <summary>The input temperature unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_TEMPERATURE_UNIT">
            <summary>The output temperature unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_THERMDILATCOEFF_UNIT">
            <summary>The input thermal dilatation coefficient unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_FACTOR_UNIT">
            <summary>The input factor unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DATE_UNIT">
            <summary>The input date unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DATE_TIME_MIN_UNIT">
            <summary>The input date time minutes unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DATE_TIME_SEC_UNIT">
            <summary>The input date time seconds unit.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_LENGTH_FRACTIONAL_IMPERIAL">
            <summary>The input length, fractional imperial.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DEFORMATION_FRACTIONAL_IMPERIAL">
            <summary>The input deformation, fractional imperial.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_DIMENSION_FRACTIONAL_IMPERIAL">
            <summary>The input dimension, fractional imperial.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.INPUT_RADIUSOFINERTIA_FRACTIONAL_IMPERIAL">
            <summary>The input radius of inertia, fractional imperial.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_LENGTH_FRACTIONAL_IMPERIAL">
            <summary>The output length, fractional imperial.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.dotdiaUnitTypes_e.OUTPUT_DEFORMATION_FRACTIONAL_IMPERIAL">
            <summary>The output deformation, fractional imperial.</summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.SettingsUnitType_e.Millimeter">
            <summary>
            The millimeter unit type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.SettingsUnitType_e.Centimeter">
            <summary>
            The centimeter unit type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.SettingsUnitType_e.Meter">
            <summary>
            The meter unit type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.SettingsUnitType_e.Inch">
            <summary>
            The inch unit type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.SettingsUnitType_e.Foot">
            <summary>
            The foot unit type.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.SettingsUnitType_e.Fractional">
            <summary>
            The foot and inch fractional unit type.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.dotdiaAttributeDataType_e">
            <summary>
            The primitive data types that TS Object Dialog tree supports
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.dotdiaAttributeDataType_e.Integer">
            <summary>
            The attribute is of type Integer
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.dotdiaAttributeDataType_e.Double">
            <summary>
            The attribute is of type Double
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.dotdiaAttributeDataType_e.String">
            <summary>
            The attribute is of type String
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.dotdiaAttributeFieldType_e">
            <summary>
            Further specification of the TS Object Dialog tree field type
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.dotdiaAttributeFieldType_e.Number">
            <summary>
            The attribute is a number
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.dotdiaAttributeFieldType_e.Text">
            <summary>
            The attribute is textual
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.CDelegate">
            <summary>
            Interface from dialog assembly to TS
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegate.#ctor">
            <summary>
            Instantiates a new CDelegate
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.CDelegate.Singletons">
            <summary>
            Storage class for singleton instances.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.CDelegate.Singletons.#cctor">
            <summary>
            Explicit static constructor to tell C# compiler
            not to mark type as beforefieldinit. Do not remove.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.Synchronize">
            <summary>
            Synchronization class where we start a for, and wait for it to complete.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.DelegateProxy">
            <summary>
            CDelegate remote class proxy.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.DelegateProxy._Instance">
            <summary>
            The remote delegate object.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DelegateProxy.#cctor">
            <summary>
            Initializes static instance variable.
            </summary>
        </member>
        <member name="P:Tekla.Structures.DialogInternal.DelegateProxy.Delegate">
            <summary>
            Gets the singleton CDelegate instance that includes model API methods.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.Remoter">
            <summary>
            Handles the .net remoting for dialog-assembly.
            </summary>
        </member>
        <member name="F:Tekla.Structures.DialogInternal.Remoter.ChannelName">
            <summary>
            Name of the remoting channel to register.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.Remoter.PublishTypes">
            <summary>
            Publishes the .net remoting types.
            </summary>
            <returns>true</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.Remoter.InitializeSandBox">
            <summary>
            Initializes the .net remoting channel
            </summary>
            <returns></returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.StructuresExtender">
            <summary>
            The StructuresExtender class is for binding controls to datatypes. The bindings are 
            needed to transfer the dialog values to Tekla Structures and onwards to plug-ins.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.CanExtend(System.Object)">
            <summary>
            Specifies whether the current object can provide its extender properties to the specified object.
            </summary>
            <param name="extendee">The object to receive the extender properties.</param>
            <returns>True if the current object can provide its extender properties to the specified object;
            otherwise, false.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.GetAttributeName(System.Windows.Forms.Control)">
            <summary>
            Retrieves the AttributeName text associated with the specified control.
            </summary>
            <param name="control">The control for which to retrieve the AttributeName text.</param>
            <returns>
            A string containing the AttributeName text for the specified control,
            or null if no text is associated with the specified control.
            </returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.SetAttributeName(System.Windows.Forms.Control,System.String)">
            <summary>
            Associates AttributeName text with the specified control.
            </summary>
            <param name="control">The control to associate the AttributeName text with.</param>
            <param name="value">The AttributeName text to associate with the control.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.GetAttributeTypeName(System.Windows.Forms.Control)">
            <summary>
            Retrieves the AttributeTypeName text associated with the specified control.
            </summary>
            <param name="control">The control for which to retrieve the AttributeTypeName text.</param>
            <returns>
            A string containing the AttributeTypeName text for the specified control,
            or null if no text is associated with the specified control.
            </returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.SetAttributeTypeName(System.Windows.Forms.Control,System.String)">
            <summary>
            Associates the AttributeTypeName text with the specified control.
            </summary>
            <param name="control">The control to associate the AttributeTypeName text with.</param>
            <param name="value">The AttributeTypeName text to associate with the control.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.GetBindPropertyName(System.Windows.Forms.Control)">
            <summary>
            Retrieves the BindPropertyName text associated with the specified control.
            </summary>
            <param name="control">The control for which to retrieve the BindPropertyName text.</param>
            <returns>
            A string containing the BindPropertyName text for the specified control,
            or null if no text is associated with the specified control.
            </returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.SetBindPropertyName(System.Windows.Forms.Control,System.String)">
            <summary>
            Associates the BindPropertyName text with the specified control.
            </summary>
            <param name="control">The control to associate the BindPropertyName text with.</param>
            <param name="value">The BindPropertyName text to associate with the control.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.GetIsFilter(System.Windows.Forms.Control)">
            <summary>
            Retrieves the IsFilter boolean value associated with the specified control.
            </summary>
            <param name="control">The control for which to retrieve the IsFilter value.</param>
            <returns>
            A boolean containing the IsFilter value for the specified control.
            </returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.StructuresExtender.SetIsFilter(System.Windows.Forms.Control,System.Boolean)">
            <summary>
            Associates the IsFilter boolean value with the specified control.
            </summary>
            <param name="control">The control to associate the IsFilter value with.</param>
            <param name="value">The IsFilter value to associate with the control.</param>
        </member>
        <member name="P:Tekla.Structures.Dialog.StructuresInstallation.BinFolder">
            <summary>
            Path to Tekla Structures binary (bin) directory
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.StructuresInstallation.InstallFolder">
            <summary>
            Path to Tekla Structures installation directory.
            </summary>
        </member>
        <member name="T:Tekla.Structures.DialogInternal.DialogDataStorage">
            <summary>
            The DialogDataStorage class handles storing data to the Tekla Structures
            Object Dialog tree and retrieving it from there.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorage.#ctor">
            <summary>
            Creates a data storage with an empty name.
            </summary>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorage.#ctor(System.String)">
            <summary>
            Creates a named data storage.
            </summary>
            <param name="name">The name of the data storage.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorage.GetVariable(Tekla.Structures.DialogInternal.AttributeInfo)">
            <summary>
            Gets the value of the named variable.
            </summary>
            <param name="info">The AttributeInfo struct that contains the variable information.</param>
            <returns>The value of the requested variable.</returns>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorage.SetVariable(Tekla.Structures.DialogInternal.AttributeInfo)">
            <summary>
            Sets a value for the named variable.
            </summary>
            <param name="info">The AttributeInfo struct that contains the variable information.</param>
        </member>
        <member name="M:Tekla.Structures.DialogInternal.DialogDataStorage.SetVariableToStack(System.String,System.Object)">
            <summary>
            Sets a value for the named variable to stack.
            </summary>
            <param name="name">The name of the variable.</param>
            <param name="data">The value of the variable.</param>
        </member>
        <member name="T:Tekla.Structures.Dialog.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.BindableRadioButton">
            <summary>
            The BindableRadioButton class represents a RadioButton control that can be bound to the dialog attributes file.
            Use "Checked" as the BindPropertyName and "Integer" as the AttributeTypeName.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BindableRadioButton.OnCreateControl">
            <summary>
            Overrides the standard OnCreateControl method in order to support the PropertyBinding.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BindableRadioButton.OnCheckedChanged(System.EventArgs)">
            <summary>
            Overrides the standard OnCheckedChanged method in order to support the PropertyBinding.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BindableRadioButton.OnClick(System.EventArgs)">
            <summary>
            Overrides the standard OnClick method in order to support the PropertyBinding.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.BindableRadioButton.Checked">
            <summary>
            Gets or sets a value indicating whether the control is checked.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.BoltCatalogSize">
            <summary>
            The BoltCatalogSize class represents a control to select the bolt size
            using the bolt catalog.
            <para>
            Bolt catalog controls always need to be in pairs, meaning in every dialog
            there has to be a BoltCatalogStandard and a BoltCatalogSize control.
            </para>
            <para>
            The property BoltCatalogStandard.LinkedBoltCatalogSize sets to which
            BoltCatalogSize the control is linked.
            </para>
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BoltCatalogSize.#ctor">
            <summary>
            Initiates a new instance of the control.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.BoltCatalogStandard">
            <summary>
            The BoltCatalogStandard class represents a control to select the bolt standard
            using the bolt catalog.
            <para>
            Bolt catalog controls always need to be in pairs, meaning in every dialog
            there has to be a BoltCatalogStandard and a BoltCatalogSize control.
            </para>
            <para>
            The property BoltCatalogStandard.LinkedBoltCatalogSize sets to which BoltCatalogSize the
            control is linked.
            </para>
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BoltCatalogStandard.#ctor">
            <summary>
            Initiates a new instance of the control.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BoltCatalogStandard.OnCreateControl">
            <summary>
            When the control is created in the parent dialog,
            enumerates the bolt catalog to fill the controls.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BoltCatalogStandard.OnSelectedIndexChanged(System.EventArgs)">
            <summary>
            When the selected index changes, updates the values
            in the BoltCatalogSize combo box.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BoltCatalogStandard.OnParentVisibleChanged(System.EventArgs)">
            <summary>
            When the parent is visible, updates the values in the
            BoltCatalogSize combo box.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.BoltCatalogStandard.OnClick(System.EventArgs)">
            <summary>
            When the control is clicked, values are not loaded from a file.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.BoltCatalogStandard.LinkedBoltCatalogSize">
            <summary>
            The BoltCatalogSize control linked to the BoltCatalogStandard control.
            Bolt catalog controls always need to be in pairs, meaning in every dialog
            there has to be a BoltCatalogStandard and a BoltCatalogSize control.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel">
            <summary>
            The OkApplyModifyGetOnOffCancel class represents a control including the Ok-Apply-Modify-Get-On/Off-Cancel button group.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.#ctor">
            <summary>
            Initializes a new instance of the OkApplyModifyGetOnOffCancel class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.OnLoad(System.EventArgs)">
            <summary>
            Localizes the control when loaded.
            </summary>
            <param name="Exc">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.OkClicked">
            <summary>
            The OkClicked event is raised when the Ok button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.ApplyClicked">
            <summary>
            The ApplyClicked event is raised when the Apply button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.ModifyClicked">
            <summary>
            The ModifyClicked event is raised when the Modify button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.GetClicked">
            <summary>
            The GetClicked event is raised when the Get button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.OnOffClicked">
            <summary>
            The OnOffClicked event is raised when the On-Off button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyModifyGetOnOffCancel.CancelClicked">
            <summary>
            The CancelClicked event is raised when the Cancel button is clicked.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.CommitAction">
            <summary>
            The CommitAction class represents a "template" dialog for commit actions.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CommitAction.#ctor">
            <summary>
            Initializes a new instance of the CommitAction class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CommitAction.OnLoad(System.EventArgs)">
            <summary>
            The commit action dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CommitAction.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CommitAction.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.groupBox">
            <summary>
            The groupBox contains the main options.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.suboptionsButton">
            <summary>
            The suboptionsButton opens the suboptions dialog.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.optionalGroupBox">
            <summary>
            The optionalGroupBox contains the optional options.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.optionalSuboptionsButton">
            <summary>
            The optionalSuboptionsButton opens the suboptions dialog for the optional
            options.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.verbButton">
            <summary>
            The verbButton (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.cancelButton">
            <summary>
            The cancelButton cancels the commit.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.optionalOptionsLabel">
            <summary>
            The optionalOptionsLabel: the optional options label (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CommitAction.optionsLabel">
            <summary>
            The optionsLabel: the options label (to be changed by the user).
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ComponentCatalog">
            <summary>
            The ComponentCatalog class represents a group of controls to select components
            using the component catalog.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalog.componentSelectionForm">
            <summary>
            New instance of the component selection dialog
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalog.selectedName">
            <summary>
            field for selected name
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalog.selectedNumber">
            <summary>
            field for selected number
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalog.selectButton">
            <summary>
            select button 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalog.#ctor">
            <summary>
            Initializes a new instance of the ComponentCatalog class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalog.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the control begins to enumerate through the component catalog.
            </summary>
            <param name="eventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalog.OnSelectClicked(System.Object,System.EventArgs)">
            <summary>
            Opens the component catalog dialog for selecting a component
            </summary>
            <param name="sender">The sender.</param>
            <param name="eventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalog.InitializeComponent">
            <summary>
            Initialization of control
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ComponentCatalog.SelectClicked">
            <summary>
            The SelectClicked event is raised when the Select button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ComponentCatalog.SelectionDone">
            <summary>
            The SelectionDone event is raised when the selection has been done.
            </summary>
            <remarks>
            Please note that when filling in controls programmatically, the bound attribute is not stored automatically.
            The attribute has to be set using the <see cref="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.Windows.Forms.Control,System.Object)"/> method of
            the <see cref="T:Tekla.Structures.Dialog.FormBase"/> class.
            </remarks>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ComponentCatalog.SelectedName">
            <summary>
            Gets or sets the selected component name in the control.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ComponentCatalog.SelectedNumber">
            <summary>
            Gets or sets the selected component number in the control.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ComponentCatalog.ComponentSelectionForm">
            <summary>
            Gets or sets new instance of the component selection dialog
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionData">
            <summary>
            Component catalog dialog data item.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionData.item">
            <summary>
            Component item.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionData.collections">
            <summary>
            Collection data of compoenent item.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionData.#ctor(Tekla.Structures.Catalogs.ComponentItem)">
            <summary>
            Initializes a new instance of the ComponentCatalogCollectionData class.
            </summary>
            <param name="newItem">The component catalog data constructor arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionData.AddToCollection(System.String)">
            <summary>
            Component catalog dialog data constructor.
            </summary>
            <param name="newCollection">The component catalog collections to be added.</param>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionData.Item">
            <summary>
            Gets component catalog component item.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionData.Collections">
            <summary>
            Gets component catalog collection information of component.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource">
            <summary>
            Component catalog dialog data source.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.componentCatalogCollectionItems">
            <summary>
            List of component catalog collection items.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.#cctor">
            <summary>
            Initializes static members of the ComponentCatalogCollectionDataSource class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.AddItem(Tekla.Structures.Catalogs.ComponentItem)">
            <summary>
            Add new Component catalog dialog data item to datasource.
            </summary>
            <param name="item">The component item to be added to catalog data dialog datasource.</param>
            <returns>True if item added successfully.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.AddCollection(System.Int32,System.String,System.String)">
            <summary>
            Add new Component catalog dialog data collection item to datasource.
            </summary>
            <param name="number">The number of the component item.</param>
            <param name="name">The name of the component item.</param>
            <param name="newCollection">The collection name to be added to the component dialog item.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.AddCollection(System.String,System.String)">
            <summary>
            Add new Component catalog dialog data collection item based on UI name to datasource.
            </summary>
            <param name="uiName">The UI name of the component item.</param>
            <param name="newCollection">The collection name to be added.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.ClearItems">
            <summary>
            Clear all items in the datasource.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.GetComponentItems(System.String)">
            <summary>
            Get Component item list based on collection string.
            </summary>
            <param name="collectionName">The collection name used to search component items from the datasource.</param>
            <returns>List of component items matching criteria.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.GetComponentItemsContainingText(System.String)">
            <summary>
            Get Component item list based on search text.
            </summary>
            <param name="searchText">The collection name used to search component items from the datasource.</param>
            <returns>List of component items matching criteria.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.GetAllPluginItems">
            <summary>
            Get all plug-in items from the datasource.
            </summary>
            <returns>List of component items matching criteria.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.GetAllCustomComponentItems">
            <summary>
            Get all custom component items from the datasource.
            </summary>
            <returns>List of component items matching criteria.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogCollectionDataSource.GetAllComponentItems">
            <summary>
            Get all component items from the datasource.
            </summary>
            <returns>List of component items matching criteria.</returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm">
            <summary>The ComponentCatalogForm class represents a form for selecting component catalog items.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.COMPONENTCATALOGFILE">
            <summary>Catalog file.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.COMPONENTCATALOGTREEVIEWFILE">
            <summary>Catalog tree file.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.SYSTEMBASICSCOMPONENTSFILESD">
            <summary>Catalog basic file for steel.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.SYSTEMBASICSCOMPONENTSFILECD">
            <summary>Catalog basic file for concrete.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.SYSTEMBASICSCOMPONENTSFILEFD">
            <summary>Catalog basic file for full.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.BASICSTRING">
            <summary>Basic string.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.COLLECTIONSTRING">
            <summary>Collection string.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.COMPONENTSTRING">
            <summary>Component string.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.selectedName">
            <summary>Selected component name.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.selectedNumber">
            <summary>Selected component number.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.connectionImage">
            <summary>Connection image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.customConnectionImage">
            <summary>Custom connection image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.detailImage">
            <summary>Detail image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.customDetailImage">
            <summary>CustomDetail image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.objectImage">
            <summary>Object image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.customObjectImage">
            <summary>Customobject image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.componentImage">
            <summary>Component image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.customComponentImage">
            <summary>Custom component image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.partImage">
            <summary>Part image.</summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.customPartImage">
            <summary>Custom part image.</summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.#ctor">
            <summary>
            Initializes a new instance of the ComponentCatalogForm class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the tree in the dialog is filled with the components.
            The create dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="eventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.InitializeImages">
            <summary>
            Initialize images.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.FillCollectionBoxStandardItems">
            <summary>
            Initialize combobox.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.ReadCatalogBasicFilesAndAddToCollection">
            <summary>
            Read catalog basic files and add to collections.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.ReadCatalogFilesAndUpdateCollectionBox">
            <summary>
            Read catalog file and add to collections.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.CreateNode(System.String)">
            <summary>
            Create node.
            </summary>
            <param name="nodeString">Node string.</param>
            <returns>Treenode if item added successfully.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.CreateTreeNode(System.Windows.Forms.TreeNode,System.String)">
            <summary>
            Create node to catalog tree.
            </summary>
            <param name="fatherNode">Father node.</param>
            <param name="nodeString">Node string.</param>
            <returns>Treenode if item added successfully.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.ReadTreeFilesAndFillComponentTree">
            <summary>
            Read catalog tree file, create tree and add to collections.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.AdjustComponentDataGridColumns">
            <summary>
            Create image column to component datagrid and organize information in the grid.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.RefreshComponentGridView">
            <summary>
            Refresh component datagrid to show correct datasource.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.ComponentCatalogForm_Shown(System.Object,System.EventArgs)">
            <summary>
            Form showed.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.CollectionBox_SelectedIndexChanged(System.Object,System.EventArgs)">
            <summary>
            Collection changed.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.SearchButton_Click(System.Object,System.EventArgs)">
            <summary>
            Search button clicked.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.SearchTextBox_TextChanged(System.Object,System.EventArgs)">
            <summary>
            Search field text changed.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.ComponentTreeView_AfterSelect(System.Object,System.Windows.Forms.TreeViewEventArgs)">
            <summary>
            Node in component tree selected.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.ChangeViewBetweenCollectionAndTree">
            <summary>
            Collection or component tree view changed.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.CollectionToolStripButton_Click(System.Object,System.EventArgs)">
            <summary>
            Collection view changed.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.TreeToolStripButton_Click(System.Object,System.EventArgs)">
            <summary>
            Component tree view changed.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.ComponentGridView_CellFormatting(System.Object,System.Windows.Forms.DataGridViewCellFormattingEventArgs)">
            <summary>
            Datagrid cell formatting. Imagevalue set based on type
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.OkCancel1_OkClicked(System.Object,System.EventArgs)">
            <summary>
            Ok clicked.
            </summary>
            <param name="sender">Event sender.</param>
            <param name="e">Event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.SelectedName">
            <summary>
            Gets the selected component name. 
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ComponentCatalogForm.SelectedNumber">
            <summary>
            Gets the selected component number. 
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.CreateDialog">
            <summary>
            The CreateDialog class represents a "template" dialog for creating something from parts.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CreateDialog.#ctor">
            <summary>
            Initializes a new instance of the CreateDialog class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CreateDialog.OnLoad(System.EventArgs)">
            <summary>
            The create dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CreateDialog.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.CreateDialog.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.createLabel">
            <summary>
            The createLabel: what is going to be created (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.allPartsRadioButton">
            <summary>
            The allPartsRadioButton creates from all parts.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.selectedPartsRadioButton">
            <summary>
            The selectedPartsRadioButton creates from selected parts.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.settingsLabel">
            <summary>
            The settingsLabel: settings related to the creation command (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.suboptionsButton">
            <summary>
            The suboptionsButton opens the dialog for suboptions.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.instructionsLabel">
            <summary>
            The instructionsLabel: brief instructions on how to use the dialog (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.cancelButton">
            <summary>
            The cancelButton cancels the creation.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.CreateDialog.createButton">
            <summary>
            The createButton creates the items.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.DataGrid">
            <summary>
            The DataGrid class represents a data grid control that can contain images.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.DataGrid.#ctor">
            <summary>
            Initializes a new instance of the DataGrid class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.EnvironmentFiles">
            <summary>
            The EnvironmentFiles class is for the paths where the attributes file will be searched for.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentFiles.GetStandardPropertyFileDirectories">
            <summary>
            Gets the paths where to look for the property files.
            </summary>
            <returns>The paths where to look for the property files.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentFiles.IsValidDirectory(System.String)">
            <summary>
            Checks if a directory is valid.
            </summary>
            <param name="directory">The directory to be checked.</param>
            <returns>True if the directory is valid.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentFiles.GetMultiDirectoryFileList(System.String)">
            <summary>
            Gets a list of files with the given extension from the default search directories.
            </summary>
            <param name="fileExtension">The file extension to be used.</param>
            <returns>A list of files with the given extension.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentFiles.GetMultiDirectoryFileList(System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Gets a list of files with the given extension from the given search directories.
            </summary>
            <param name="searchDirectories">The search directories to be used.</param>
            <param name="fileExtension">The file extension to be used.</param>
            <returns>A list of found file names without the file extension.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentFiles.GetAttributeFile(System.String)">
            <summary>
            Gets a file info representing the first match in the standard property file directories.
            </summary>
            <param name="fileName">The name of the file including the file extension.</param>
            <returns>A file info for the first match in the directory list. Null if no match was found.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentFiles.GetAttributeFile(System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Gets a file info representing the first match in the search directories.
            </summary>
            <param name="searchDirectories">The list of directories to be used for searching for the file.</param>
            <param name="fileName">The name of the file including the file extension.</param>
            <returns>A file info for the first match in the directory list. Null if no match was found.</returns>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.EnvironmentFiles.PropertyFileDirectories">
            <summary>
            The directories where to look for property files.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.EnvironmentVariables">
            <summary>
            The EnvironmentVariables class contains a sorted list specializing in getting active
            environment variables and advanced option settings. It also checks options.ini files
            in the active model folder as well as options_user.ini files.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentVariables.GetEnvironmentVariable(System.String)">
            <summary>
            Gets an environment variable.
            </summary>
            <param name="variableName">The name of the variable to get.</param>
            <returns>The environment variable.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentVariables.Add(System.String)">
            <summary>
            Adds a variable to the list.
            </summary>
            <param name="key">The variable to be added.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.EnvironmentVariables.Get(System.String)">
            <summary>
            Gets a key.
            </summary>
            <param name="key">The key to get.</param>
            <returns>The key.</returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ImageComboBox">
            <summary>
            The ImageComboBox class represents a combo box control that can contain images.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageComboBox.#ctor">
            <summary>
            Initializes a new instance of the ImageComboBox class.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageComboBox.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageComboBox.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageComboBox.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageComboBox.OptionList">
            <summary>
            Gets or sets the elements of the option list, the images that are in the image combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageComboBox.SelectedIndex">
            <summary>
            Gets or sets the selected index of the image in the image combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageComboBox.SelectedItem">
            <summary>
            Gets or sets the selected image in the image combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageComboBox.Size">
            <summary>
            Gets or sets the size of the image combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageComboBox.DefaultValue">
            <summary>
            Gets or sets the default value in the image combo box.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ImageComboBox.ImageCBSelectedIndexChanged">
            <summary>
            The ImageCBSelectedIndexChanged event is triggered just after the selected index has been changed in the image combo box.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ImageList">
            <summary>
            The ImageList class contains a list of ImageItems.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ImageItem">
            <summary>
            The ImageItem class defines the images that will be contained in the image combo box.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageItem.#ctor">
            <summary>
            Creates a new instance of the ImageItem class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageItem.#ctor(System.Drawing.Image,System.String)">
            <summary>
            Creates a new instance of the ImageItem class.
            </summary>
            <param name="Image">The image to be used to create the new ImageItem.</param>
            <param name="Name">The name of the new ImageItem.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageItem.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <exclude/>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageItem.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <exclude/>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageItem.Picture">
            <summary>
            Gets or sets the image. 
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageItem.Name">
            <summary>
            Gets or sets the image name.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ImageListComboBox">
            <summary>
            The ImageListComboBox class represents a combo box control that can contain images from an ImageList control.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ComboBoxRightBorder">
            <summary>
            Combo box right border size in pixel.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ComboBoxBottomBorder">
            <summary>
            Combo box bottom border in pixel.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.#ctor">
            <summary>
            Initializes a new instance of the ImageListComboBox class.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox._HoverColor">
            <summary>
            The hover color.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox._DefaultValue">
            <summary>
            The default value.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox._SelectedIndex">
            <summary>
            The selected index.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox._SelectedItem">
            <summary>
            The selected item.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox._ToolTip">
            <summary>
            The Tooltip.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox._ImageList">
            <summary>
            The source image list associated to the combo box.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox._InternalImageList">
            <summary>
            The internal image list used as a copy of the associated image list.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.OnImageCbSelectedIndexChanged(System.Object,System.EventArgs)">
            <summary>
            Called when Image ComboBox selected index changed.
            </summary>
            <param name="Sender">The sender.</param>
            <param name="E">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ComboBox_MouseWheel(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            Handles the MouseWheel event of the ComboBox control.
            </summary>
            <param name="Sender">The source of the event.</param>
            <param name="E">The <see cref="T:System.Windows.Forms.MouseEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ComboBoxDrawItem(System.Object,System.Windows.Forms.DrawItemEventArgs)">
            <summary>
            Draws the combo box item.
            </summary>
            <param name="Sender">The sender.</param>
            <param name="E">The <see cref="T:System.Windows.Forms.DrawItemEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ComboBox_MouseEnter(System.Object,System.EventArgs)">
            <summary>
            Handles the MouseEnter event of the ComboBox control.
            </summary>
            <param name="Sender">The source of the event.</param>
            <param name="E">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ComboBox_MouseLeave(System.Object,System.EventArgs)">
            <summary>
            Handles the MouseLeave event of the ComboBox control.
            </summary>
            <param name="Sender">The source of the event.</param>
            <param name="E">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ImageListComboBox_Load(System.Object,System.EventArgs)">
            <summary>
            Handles the Load event of the ImageListComboBox control.
            </summary>
            <param name="Sender">The source of the event.</param>
            <param name="E">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.RefreshOptionList">
            <summary>
            Refreshes the option list content.
            </summary>
            <example>
            The following example demonstrates how to programmatically add a new item to the combo box:
            <code>
                   using System;
                   using System.Drawing;
                   using System.Windows.Forms;
                   using Tekla.Structures.Dialog.UIControls;
            
                   public class ImageListComboBoxExample
                   {
                       private ImageListComboBox ImageListComboBox;
            
                       public ImageListComboBoxExample()
                       {
                           ImageListComboBox = new ImageListComboBox();
                       }
            
                       void AddNewImage()
                       {
                           ImageListComboBox.ImageList.Images.Add("ImageKey", new Bitmap(@"C:\Image.bmp"));
                           ImageListComboBox.RefreshOptionList();
                       }
                   }
            </code>
            </example>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.GetMaximumImageSize">
            <summary>
            Gets the maximum size of the image.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ResizeToFitImages">
            <summary>
            Resizes to fit images.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.BindImageListToComboBox">
            <summary>
            Binds the image list to combo box.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.CreateInternalImageList(System.Windows.Forms.ImageList)">
            <summary>
            Creates the internal image list.
            </summary>
            <param name="Value">The value.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.IsImageListNullOrEmpty">
            <summary>
            Determines whether the ImageList is null or empty.
            </summary>
            <returns>
              <c>true</c> if the ImageList is null or empty otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ImageListComboBox.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ImageListComboBox.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ImageListComboBoxSelectedIndexChanged">
            <summary>
            The ImageListComboBoxSelectedIndexChanged event is triggered just after the selected index has been changed in the image combo box.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ImageListComboBoxMouseWheel">
            <summary>
            The ImageListComboBoxMouseWheel event is triggered just after the mouse wheel has been activated on top of the combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ToolTipText">
            <summary>
            Gets or sets the tool tip text.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageListComboBox.ImageList">
            <summary>
            Gets or sets the ImageList that contains the images to be displayed in the combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageListComboBox.HoverColor">
            <summary>
            Gets or sets the hover color of the combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageListComboBox.SelectedIndex">
            <summary>
            Gets or sets the selected index of the image in the image combo box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageListComboBox.SelectedItem">
            <summary>
            Gets or sets the selected image in the image combo box. If the image is not found,
            SelectedIndex is used instead.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ImageListComboBox.DefaultValue">
            <summary>
            Gets or sets the default value in the image combo box. If the value is not found,
            SelectedItem is used instead.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.LoadingForm">
            <summary>
            The LoadingForm class creates a dialog that is shown while something is 
            being loaded by the main window and that needs the process to finish.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.LoadingForm.#ctor">
            <summary>
            Creates a new instance of the form.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.LoadingForm.OnClosing(System.ComponentModel.CancelEventArgs)">
            <summary>
            Prevents the loading dialog from being closed.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.LoadingForm.OnLoad(System.EventArgs)">
            <summary> 
            Localizes the control when loaded.
            </summary>
            <param name="Exc">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.LoadingForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.LoadingForm.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.LoadingForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.LoadingForm.AllowClosing">
            <summary>
            Allows the closing of the dialog. By default disabled.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.LocalizeForm">
            <summary>
            The LocalizeForm class is for localizing the forms.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.LocalizeForm.Localization">
            <summary>
            Gets the localization for the dialog.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.MaterialCatalog">
            <summary>
            The MaterialCatalog class represents a group of controls to select materials
            using the material catalog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialCatalog.#ctor">
            <summary>
            Initiates a new instance of the control.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialCatalog.#ctor(System.String)">
            <summary>
            Creates a new instance of the MaterialCatalog and will select
            the material (if available) in the tree.
            </summary>
            <param name="Material">The material to be selected in the dialog.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialCatalog.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the control begins to enumerate through the material
            catalog.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.MaterialCatalog.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialCatalog.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialCatalog.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.MaterialCatalog.SelectedMaterial">
            <summary>
            Gets the selected material in the control.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.MaterialCatalog.SelectClicked">
            <summary>
            The SelectClicked event is raised when the Select button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.MaterialCatalog.SelectionDone">
            <summary>
            The SelectionDone event is raised when the selection has been done.
            </summary>
            <remarks>
            Please note that when filling in controls programmatically, the bound attribute is not stored automatically.
            The attribute has to be set using the <see cref="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.Windows.Forms.Control,System.Object)"/> method of
            the <see cref="T:Tekla.Structures.Dialog.FormBase"/> class.
            </remarks>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm">
            <summary>
            The MaterialSelectionForm class represents a dialog to select materials
            using the material catalogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.MaterialItem})">
            <summary>
            Creates a new instance of the MaterialSelectionForm.
            </summary>
            <param name="Materials">The materials to be added to the dialog.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.MaterialItem},System.String)">
            <summary>
            Creates a new instance of the MaterialSelectionForm, selecting the given material in the tree control.
            </summary>
            <param name="Materials">The materials to be added to the dialog.</param>
            <param name="Material">The material to be selected in the tree.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the tree in the dialog is filled with the materials.
            The create dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.CreateNodeIfNeeded(System.String)">
            <summary>
            Creates a new node for material types if needed. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.CreateNewNode(System.String,System.String)">
            <summary>
            Creates a new node for material. 
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.MaterialSelectionForm.SelectedMaterial">
            <summary>
            The selected material in the control.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.MeshCatalog">
            <summary>
            The MeshCatalog class represents a group of controls to select meshes
            using the mesh catalogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshCatalog.#ctor">
            <summary>
            Initiates a new instance of the control.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshCatalog.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the control begins to enumerate through the rebar
            catalog.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshCatalog.OnSelectClicked(System.Object,System.EventArgs)">
            <summary>
            Opens the profile catalog dialog for selecting a mesh
            </summary>
            <param name="Sender"></param>
            <param name="EventArgs"></param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.MeshCatalog.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshCatalog.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshCatalog.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.MeshCatalog.SelectedMeshName">
            <summary>
            The mesh name in the control.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.MeshCatalog.SelectedMeshGrade">
            <summary>
            The mesh grade in the control.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.MeshCatalog.SelectClicked">
            <summary>
            The SelectClicked event is raised when the Select button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.MeshCatalog.SelectionDone">
            <summary>
            The SelectionDone event is raised when the selection has been done.
            </summary>
            <remarks>
            Please note that when filling in controls programmatically, the bound attribute is not stored automatically.
            The attribute has to be set using the <see cref="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.Windows.Forms.Control,System.Object)"/> method of
            the <see cref="T:Tekla.Structures.Dialog.FormBase"/> class.
            </remarks>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.MeshSelectionForm">
            <summary>
            The MeshSelectionForm class represents a dialog to select meshes
            using the mesh catalog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.MeshItem})">
            <summary>
            Creates a new instance of the MeshSelectionForm. 
            </summary>
            <param name="MeshList">A list with the meshes that will be added to the tree.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.MeshItem},System.String,System.String)">
            <summary>
            Creates a new instance of the MeshSelectionForm and will select in the tree 
            the mesh that contains the given parameters (if possible).
            </summary>
            <param name="MeshList">A list with the meshes that will be added to the tree.</param>
            <param name="StartingMesh">The name of the mesh to be selected.</param>
            <param name="StartingGrade">The grade of the mesh to be selected.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the tree in the dialog is filled with the rebars.
            The create dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="E">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.CreateNodeIfNeeded(Tekla.Structures.Catalogs.MeshItem,System.Int32)">
            <summary>
            Creates new nodes in the tree for names of the meshes.
            </summary>
            <param name="Item">MeshItem that is going to be added to the tree if needed.</param>
            <param name="NodeType">NodeType grade or name of the mesh to be added to the tree.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.CancelClicked(System.Object,System.EventArgs)">
            <summary>
            When Cancel button is clicked dialog is closed and not rebar is returned.
            </summary>
            <param name="Sender"></param>
            <param name="E"></param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.OkClicked(System.Object,System.EventArgs)">
            <summary>
            When Ok button is clicked dialog is closed and select rebar is returned.
            </summary>
            <param name="Sender"></param>
            <param name="EventArgs"></param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.SelectedMeshName">
            <summary>
            The selected mesh name.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.MeshSelectionForm.SelectedMeshGrade">
            <summary>
            The selected mesh grade.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ModelAccess">
            <summary>
            The ModelAccess class contains helper methods for connecting to and accessing the model and objects in the model.
            The class attempts to provide efficient but robust methods for connecting to and verifying the connection to the model.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ModelAccess.ConnectToModel">
            <summary>
            Gets a model connnection.
            </summary>
            <returns>The model or null if unable to connect.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ModelAccess.ConnectToModel(Tekla.Structures.Model.Model@)">
            <summary>
            Gets a model connection.
            </summary>
            <param name="model">The model connection.</param>
            <returns>True on success. False otherwise.</returns>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ModelAccess.ConnectToModel(System.Boolean@)">
            <summary>
            Gets a model connection.
            </summary>
            <param name="ConnectedToModel">True if a model connection was made. False otherwise.</param>
            <returns>The model or null if unable to connect.</returns>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.OkApplyCancel">
            <summary>
            The OkApplyCancel class represents a control including the Ok-Apply-Cancel button group.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyCancel.#ctor">
            <summary>
            Initializes a new instance of the OkApplyCancel class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyCancel.OnLoad(System.EventArgs)">
            <summary>
            Localizes the control when loaded.
            </summary>
            <param name="Exc">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OkApplyCancel.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyCancel.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkApplyCancel.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyCancel.OkClicked">
            <summary>
            The OkClicked event is raised when the Ok button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyCancel.ApplyClicked">
            <summary>
            The ApplyClicked event is raised when the Apply button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkApplyCancel.CancelClicked">
            <summary>
            The CancelClicked event is raised when the Cancel button is clicked.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.OkCancel">
            <summary>
            The OkCancel class represents a control including the Ok-Cancel button group.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkCancel.#ctor">
            <summary>
            Initializes a new instance of the OkCancel class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkCancel.OnLoad(System.EventArgs)">
            <summary>
            Localizes the control when loaded.
            </summary>
            <param name="Exc">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OkCancel.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkCancel.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OkCancel.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkCancel.OkClicked">
            <summary>
            The OkClicked event is raised when the Ok button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.OkCancel.CancelClicked">
            <summary>
            The CancelClicked event is raised when the Cancel button is clicked.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.OrganizerDialog">
            <summary>
            The OrganizerDialog class represents a "template" of an organizer dialog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OrganizerDialog.#ctor">
            <summary>
            Initializes a new instance of the OrganizerDialog class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OrganizerDialog.OnLoad(System.EventArgs)">
            <summary>
            The organizer dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OrganizerDialog.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.OrganizerDialog.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.closeButton">
            <summary>
            The closeButton closes the dialog.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.listView">
            <summary>
            The listView contains items to be organized (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.button1">
            <summary>
            The button1 (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.button3">
            <summary>
            The button3 (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.button4">
            <summary>
            The button4 (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.instructionsLabel">
            <summary>
            The instructionsLabel contains intructions for the dialog (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.columnHeader1">
            <summary>
            The columnHeader1 (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.columnHeader2">
            <summary>
            The columnHeader2 (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.columnHeader3">
            <summary>
            The columnHeader3 (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.OrganizerDialog.contextMenuStrip1">
            <summary>
            The contextMenuStrip1 (to be changed by the user).
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ProfileCatalog">
            <summary>
            The ProfileCatalog class represents a group of controls to select profiles
            using the profile catalogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileCatalog.#ctor">
            <summary>
            Initiates a new instance of the control.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileCatalog.#ctor(System.String)">
            <summary>
            Initiates a new instance of the control selecting the given profile.
            </summary>
            <param name="Profile">The profile string that will 
            be selecteed in the dialog.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileCatalog.OnSelectClicked(System.Object,System.EventArgs)">
            <summary>
            Opens the profile catalog dialog for selecting a profile.
            </summary>
            <param name="Sender"></param>
            <param name="EventArgs"></param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileCatalog.OnLoad(System.EventArgs)">
            <summary>
            The profile catalog control is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ProfileCatalog.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileCatalog.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileCatalog.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ProfileCatalog.SelectedProfile">
            <summary>
            The selected profile in the control.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ProfileCatalog.SelectClicked">
            <summary>
            The SelectClicked event is raised when the Select button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ProfileCatalog.SelectionDone">
            <summary>
            The SelectionDone event is raised when the profile selection dialog is closed.
            </summary>
            <remarks>
            Please note that when filling in controls programmatically, the bound attribute is not stored automatically.
            The attribute has to be set using the <see cref="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.Windows.Forms.Control,System.Object)"/> method of
            the <see cref="T:Tekla.Structures.Dialog.FormBase"/> class.
            </remarks>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm">
            <summary>
            The ProfileSelectionForm class represents a dialog to select profiles
            using the profile catalogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.LibraryProfileItem},System.Collections.Generic.List{Tekla.Structures.Catalogs.ParametricProfileItem})">
            <summary>
            Creates a new instance of the ProfileSelectionForm. 
            </summary>
            <param name="LibraryProfiles">The library profiles to be added to the dialog.</param>
            <param name="ParametricProfiles">The parametric profiles to be added to the dialog.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.LibraryProfileItem},System.Collections.Generic.List{Tekla.Structures.Catalogs.ParametricProfileItem},System.String)">
            <summary>
            Creates a new instance of the ProfileSelectionForm selecting the given profile in the tree control. 
            </summary>
            <param name="LibraryProfileItems">The library profiles to be added to the dialog.</param>
            <param name="ParametricProfileItems">The parametric profiles to be added to the dialog.</param>
            <param name="Profile">The profile that will be selected in the tree.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the enumeration through the catalogs begins.
            The create dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.SetUpImagesInTree">
            <summary>
            Sets the images that will be used in the tree view. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.FillTreeWithProfiles">
            <summary>
            Enumerate through the catalogs and fill the tree control. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.CreateNewNode(System.String,System.String)">
            <summary>
            Creates a new node for profile names if needed (HEA for HEA300). 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.AddProfileToTree(Tekla.Structures.Catalogs.LibraryProfileItem,System.String)">
            <summary>
            Adds a library profile to the tree. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.AddProfileToTree(Tekla.Structures.Catalogs.ParametricProfileItem)">
            <summary>
            Adds a parametric profile to the tree. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.AddImperialProfileToTree(Tekla.Structures.Catalogs.ParametricProfileItem)">
            <summary>
            Adds a parametric profile to the tree. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.GetListOfParameters(System.String,System.String[]@)">
            <summary>
            Gets the parameters from the ParameterString. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.CreateProfileTypeNode(Tekla.Structures.Catalogs.ProfileItem.ProfileItemTypeEnum)">
            <summary>
            Creates a profile type node if needed (e.g. I profile). 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.CancelClicked(System.Object,System.EventArgs)">
            <summary>
            If cancel is clicked no profile is passed. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.OkClicked(System.Object,System.EventArgs)">
            <summary>
            Closes the dialog. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.GetProfileStringInCurrentUnits(System.String)">
            <summary>
            Selects parametric profile item and converts values in profile string into used UI units. 
            Returns library profile item back without any changes. 
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.tree_AfterSelect(System.Object,System.Windows.Forms.TreeViewEventArgs)">
            <summary>
            Handles event if a value is selected in the tree view control. 
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.SelectedProfile">
            <summary>
            The selected profile.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ProfileSelectionForm.UseImperialUnits">
            <summary>
            Units used
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.PropertiesDialog">
            <summary>
            The PropertiesDialog class represents a "template" of a properties dialog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.PropertiesDialog.#ctor">
            <summary>
            Initializes a new instance of the PropertiesDialog class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.PropertiesDialog.OnLoad(System.EventArgs)">
            <summary>
            The properties dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.PropertiesDialog.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.PropertiesDialog.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.instruccionsLabel">
            <summary>
            The instruccionsLabel contains instructions for the dialog (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.propertiesTabControl">
            <summary>
            The propertiesTabControl: tab control containing properties.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.mainGroupBox">
            <summary>
            The mainGroupBox (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.optionalGroupBox">
            <summary>
            The optionalGroupBox (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.optionalContentsLabel">
            <summary>
            The optionalContentsLabel (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.mainContentsLabel">
            <summary>
            The mainContentsLabel (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.tabPage1">
            <summary>
            The tabPage1: options tab page (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.tabPage2">
            <summary>
            The tabPage2: options tab page (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.PropertiesDialog.okCancel">
            <summary>
            The okCancel: group of buttons for Ok/Cancel.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog">
            <summary>
            The ReinforcementCatalog class represents a group of controls to select rebars
            using the rebar catalogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.#ctor">
            <summary>
            Initiates a new instance of the control.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.#ctor(System.String,System.String,System.Double)">
            <summary>
            Creates a new instance of the ReinforcementCatalog and will select in the tree 
            the rebar that contains the given parameters (if possible).
            </summary>
            <param name="Size">The size of the rebar to be selected.</param>
            <param name="Grade">The grade of the rebar to be selected.</param>
            <param name="BendRadius">The bend radius of the rebar to be selected.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog._RebarSelectionForm">
            <summary>
            New instance of the reinforcement selection dialog
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the control begins to enumerate through the rebar
            catalog.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.OnSelectClicked(System.Object,System.EventArgs)">
            <summary>
            Opens the profile catalog dialog for selecting a profile
            </summary>
            <param name="Sender"></param>
            <param name="EventArgs"></param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.SelectedRebarSize">
            <summary>
            Gets the selected rebar size in the control.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.SelectedRebarGrade">
            <summary>
            Gets the selected rebar grade in the control.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.SelectedRebarBendingRadius">
            <summary>
            Gets the selected rebar bending radius in the control.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.RebarSelectionForm">
            <summary>
            New instance of the reinforcement selection dialog
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.SelectClicked">
            <summary>
            The SelectClicked event is raised when the Select button is clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.ReinforcementCatalog.SelectionDone">
            <summary>
            The SelectionDone event is raised when the selection has been done.
            </summary>
            <remarks>
            Please note that when filling in controls programmatically, the bound attribute is not stored automatically.
            The attribute has to be set using the <see cref="M:Tekla.Structures.Dialog.FormBase.SetAttributeValue(System.Windows.Forms.Control,System.Object)"/> method of
            the <see cref="T:Tekla.Structures.Dialog.FormBase"/> class.
            </remarks>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm">
            <summary>
            The ReinforcementSelectionForm class represents a dialog to select rebars
            using the rebar catalogs.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.RebarItem})">
            <summary>
            Creates a new instance of the ReinforcementSelectionForm. 
            </summary>
            <param name="RebarList">A list with the rebars that will be added to the tree.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.#ctor(System.Collections.Generic.List{Tekla.Structures.Catalogs.RebarItem},System.String,System.String,System.Double)">
            <summary>
            Creates a new instance of the ReinforcementSelectionForm and will select in the tree 
            the rebar that contains the given parameters (if possible).
            </summary>
            <param name="RebarList">A list with the rebars that will be added to the tree.</param>
            <param name="Size">The size of the rebar to be selected.</param>
            <param name="Grade">The grade of the rebar to be selected.</param>
            <param name="BendRadius">The bend radius of the rebar to be selected.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.OnLoad(System.EventArgs)">
            <summary>
            When the load event is raised, the tree in the dialog is filled with the rebars.
            The create dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="EventArgs">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.CreateNodeIfNeeded(Tekla.Structures.Catalogs.RebarItem,System.Int32)">
            <summary>
            Creates new nodes in the tree for Grades and Usages of the rebar
            </summary>
            <param name="Item">RebarItem that is going to be added to the tree if needed</param>
            <param name="NodeType">Node type that is going to be added</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.CancelClicked(System.Object,System.EventArgs)">
            <summary>
            When Cancel button is clicked dialog is closed and not rebar is returned
            </summary>
            <param name="Sender"></param>
            <param name="EventArgs"></param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.OkClicked(System.Object,System.EventArgs)">
            <summary>
            When Ok button is clicked dialog is closed and select rebar is returned
            </summary>
            <param name="Sender"></param>
            <param name="EventArgs"></param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.SelectedSize">
            <summary>
            Gets the selected rebar size. 
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.SelectedGrade">
            <summary>
            Gets the selected rebar grade. 
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.ReinforcementSelectionForm.SelectedBendingRadius">
            <summary>
            Gets the selected rebar bending radius. 
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.SaveLoad">
            <summary>
            The SaveLoad class represents a save-load-save as group of controls including the functionality.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.SaveLoad._fileExtension">
            <summary>
            File extension.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.SaveLoad._helpUrl">
            <summary>
            Help url.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.SaveLoad._helpKeyword">
            <summary>
            Help keyword.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Tekla.Structures.Dialog.UIControls.SaveLoad"/> class with the default language and file extensions.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.Dispose(System.Boolean)">
            <summary> 
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.OnLoad(System.EventArgs)">
            <summary> 
            When the control is loaded, checks if the parent was loaded in order to update it.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.InitializeAttributeFileDirectories">
            <summary>
            Initializes the list of search directories to the standard Tekla search directories (model, XS_PROJECT, XS_FIRM, system)
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.ComboBoxFileListUpdate">
            <summary>
            Updates or re-populates the file list
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.OnSaveClicked(System.Object,System.EventArgs)">
            <summary>
            Saves the dialog properties to the currently selected file name and calls any AttributesSaved events for additional save related routines.
            </summary>
            <param name="sender">Event sender</param>
            <param name="e">Event arguments</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.LoadStandardFileAndApplyValues(Tekla.Structures.Dialog.FormBase,System.String)">
            <summary>
            Loads the dialog values from the file and performs apply on the loaded values.
            </summary>
            <param name="form">Dialog form</param>
            <param name="fileName">File name</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.OnLoadClicked(System.Object,System.EventArgs)">
            <summary>
            Loads the dialog properties from the currently selected file name and calls any AttributesLoading and AttributesLoaded events for additional load related routines.
            </summary>
            <param name="sender">Event sender</param>
            <param name="e">Event arguments</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.OnSaveAsClicked(System.Object,System.EventArgs)">
            <summary>
            Saves the dialog properties to the save as file name and calls any AttributesSaved events for additional save related routines.
            </summary>
            <param name="sender">Event sender</param>
            <param name="e">Event arguments</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.OnHelpClicked(System.Object,System.EventArgs)">
            <summary>
            Opens the help file. 
            When General help file type is selected, HelpUrl and HelpKeyword can be set as properties by the father application.
            If UserDefined help file type is selected, UserDefinedHelpFilePath can be set as property by the father application.
            </summary>
            <param name="sender">Event sender</param>
            <param name="e">Event arguments</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.OnParentLoaded(System.Object,System.EventArgs)">
            <summary> 
            When control is loaded checks if the parent was loaded to update it.
            </summary>
            <param name="sender">Event sender</param>
            <param name="e">Event arguments</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.InitializeFileList">
            <summary>
            Updates (populates) the file list and if a "standard" file exists, loads it.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.InitializeSaveAsText(System.String)">
            <summary>
            Updates (populates) the text in "save as" text box.
            </summary>
            <param name="saveAsText">Text to be inserted to "save as" text box.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.SaveValuesToXml(System.String)">
            <summary>
            Saves control's values to an xml file.
            </summary>
            <param name="fileName">File name</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.TextBoxSaveAsTextChanged(System.Object,System.EventArgs)">
            <summary>
            If textBoxSaveAs contains invalid characters saveAsButton is disabled.
            </summary>
            <param name="sender">Event sender</param>
            <param name="e">Event arguments</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.ComboBoxFileListTextChanged(System.Object,System.EventArgs)">
            <summary>
            If comboBoxFileList contains invalid characters loadButton is disabled.
            </summary>
            <param name="sender">Event sender</param>
            <param name="e">Event arguments</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.SaveLoad.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.SaveLoad.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.AttributesLoaded">
            <summary>
            The AttributesLoaded event is triggered just after attributes have been loaded into the dialog.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.AttributesSaved">
            <summary>
            The AttributesSaved event is triggered just after attributes are saved to a file.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.AttributesSavedAs">
            <summary>
            The AttributesSavedAs event is triggered just after attributes are "saved as" to a file.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpOpened">
            <summary>
            The HelpOpened event is triggered just after the help file is opened.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.AttributesLoadClicked">
            <summary>
            The AttributesLoadClicked event is triggered just after the load button has been clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.AttributesSaveClicked">
            <summary>
            The AttributesSaveClicked event is triggered just after the save button has been clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.AttributesSaveAsClicked">
            <summary>
            The AttributesSaveAsClicked event is triggered just after the save as button has been clicked.
            </summary>
        </member>
        <member name="E:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpOpenClicked">
            <summary>
            The HelpOpenClicked event is triggered just after the help button has been clicked.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.SaveAsText">
            <summary>
            Gets or sets the text in the SaveAs text box.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.SaveLoadText">
            <summary>
            Gets the currently selected file name in the Save/Load combo box. 
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpUrl">
            <summary>
            Gets or sets the HelpUrl where the help file is located.
            If you do not provide a HelpUrl, the Tekla Structures help viewer will be displayed with given HelpKeyword.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpKeyword">
            <summary>
            Gets or sets the HelpKeyword that the help file should be opened for.
            If you do not provide a HelpKeyword and HelpUrl is not set, the default 
            start page for Tekla Structures Help viewer is displayed.
            </summary>
            <example>
            If you would like to show for example the topic in the following location:
            "C:\MyApp\Sample.chm::/SubBook.chm::/MainPage.htm", the HelpKeyword
            would be "SubBook.chm::/MainPage.htm".
            
            If you are using the HelpProvider class to provide pop-up or online help for controls,
            you can get the keyword by calling HelpProvider1.GetHelpKeyword(this).
            
            If you would like to use Tekla Structures Help Viewer, you can do so by specifying HelpKeyword as "Filename"
            without extension and not setting the HelpUrl. 
            
            </example>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpFileType">
            <summary>
            Gets or sets the help file type.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.UserDefinedHelpFilePath">
            <summary>
            Gets or sets the file path where the UserDefined help file is located.
            If you do not provide a UserDefinedHelpFilePath, the general Help file, help.chm, will be displayed.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.AttributeFileDirectories">
            <summary>
            Gets or sets the attribute file directories.
            </summary>
        </member>
        <member name="P:Tekla.Structures.Dialog.UIControls.SaveLoad.FileExtension">
            <summary>
            Gets or sets the file extension for the attribute files loaded and saved by the control.
            </summary>
            <value>The file extension.</value>
            <remarks>Note that any "." characters will be removed from the beginning and end of the file extension.</remarks>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpFileTypeEnum">
            <summary>
            The help file types. 
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpFileTypeEnum.General">
            <summary>
            The general help file; help.chm.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.SaveLoad.HelpFileTypeEnum.UserDefined">
            <summary>
            The user defined help file.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.Sorting.TeklaSort">
            <summary>
            An IComparer interface to sort lists in the same way that Tekla attribute and report lists are sorted.
            </summary>
            <remarks>Tekla Structures sorting places lowercase letters before uppercase letters.
            In all other cases, the local cultural info is used for sorting.
            Note that using this comparer will tread upper and lowercase letters as different.</remarks>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.Tree">
            <summary>
            The Tree class represents a tree view control that can contain images.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.Tree.#ctor">
            <summary>
            Initializes a new instance of the Tree class.
            </summary>
        </member>
        <member name="T:Tekla.Structures.Dialog.UIControls.TreeViewDialog">
            <summary>
            The TreeViewDialog class represents a "template" of a tree view dialog.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.TreeViewDialog.#ctor">
            <summary>
            Initializes a new instance of the TreeViewDialog class.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.TreeViewDialog.OnLoad(System.EventArgs)">
            <summary>
            The tree view dialog is only localized when it is not in design mode,
            meaning it is shown without localization in Visual Studio.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.TreeViewDialog.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.TreeViewDialog.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing">True if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Tekla.Structures.Dialog.UIControls.TreeViewDialog.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.TreeViewDialog.treeView">
            <summary>
            The treeView: tree view containing the different nodes (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.TreeViewDialog.propertiesLabel">
            <summary>
            The propertiesLabel: properties of the selected node (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.TreeViewDialog.instructionsLabel">
            <summary>
            The instructionsLabel contains instructions for the dialog (to be changed by the user).
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.TreeViewDialog.okCancel">
            <summary>
            The okCancel: group of buttons for Ok/Cancel.
            </summary>
        </member>
        <member name="F:Tekla.Structures.Dialog.UIControls.TreeViewDialog.contextMenuStrip1">
            <summary>
            The contextMenuStrip1 (to be changed by the user).
            </summary>
        </member>
    </members>
</doc>
