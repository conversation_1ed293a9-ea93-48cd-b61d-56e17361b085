using System;
using System.IO;
using System.Text;

namespace TeklaTool.Utils
{
    public static class Logger
    {
        private static readonly string LogFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "TeklaTool", "logs", $"log_{DateTime.Now:yyyyMMdd}.txt");

        private static readonly object LockObject = new object();

        /// <summary>
        /// 日志开关，默认关闭以减少性能开销
        /// </summary>
        public static bool IsEnabled { get; set; } = false;

        static Logger()
        {
            // 确保日志目录存在
            string logDirectory = Path.GetDirectoryName(LogFilePath);
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
        }

        public static void LogInfo(string message)
        {
            Log("INFO", message);
        }

        public static void LogError(string message)
        {
            Log("ERROR", message);
        }

        public static void LogWarning(string message)
        {
            Log("WARNING", message);
        }

        private static void Log(string level, string message)
        {
            // 如果日志功能未启用，直接返回
            if (!IsEnabled)
            {
                return;
            }

            try
            {
                lock (LockObject)
                {
                    using (StreamWriter writer = new StreamWriter(LogFilePath, true, Encoding.UTF8))
                    {
                        writer.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [{level}] {message}");
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果日志记录失败，我们不能再次调用日志记录，否则会导致递归
                Console.WriteLine($"日志记录失败: {ex.Message}");
            }
        }
    }
}
